"""
Anthropic Claude Provider Implementation
Handles communication with <PERSON><PERSON><PERSON>'s <PERSON> models
"""

import asyncio
from typing import Dict, Any, List
from .base_provider import <PERSON>Provider, AIRequest, AIResponse, TaskType
import anthropic

class <PERSON><PERSON><PERSON><PERSON>(AIProvider):
    """Anthropic Claude provider implementation"""
    
    def _initialize(self):
        """Initialize Claude client"""
        try:
            self.client = anthropic.AsyncAnthropic(api_key=self.api_key)
            self.is_available = True
        except Exception as e:
            self.is_available = False
            print(f"Failed to initialize Claude provider: {e}")
    
    async def generate_response(self, request: AIRequest) -> AIResponse:
        """Generate response using Anthropic Claude"""
        try:
            # Prepare messages
            messages = []
            if request.system_message:
                messages.append({"role": "user", "content": f"System: {request.system_message}"})
            
            messages.append({"role": "user", "content": request.prompt})
            
            # Prepare parameters
            params = {
                "model": self._get_best_model_for_task(request.task_type),
                "messages": messages,
                "max_tokens": request.max_tokens or 4000,
                "temperature": request.temperature
            }
            
            # Make API call
            response = await self.client.messages.create(**params)
            
            # Extract response content
            content = response.content[0].text
            
            # Calculate cost
            cost = self._calculate_cost(
                response.usage.input_tokens,
                response.usage.output_tokens,
                params["model"]
            )
            
            return AIResponse(
                content=content,
                provider="claude",
                model=params["model"],
                tokens_used=response.usage.input_tokens + response.usage.output_tokens,
                cost=cost,
                metadata={
                    "input_tokens": response.usage.input_tokens,
                    "output_tokens": response.usage.output_tokens,
                    "stop_reason": response.stop_reason
                }
            )
            
        except Exception as e:
            raise Exception(f"Claude API error: {str(e)}")
    
    def get_models(self) -> List[str]:
        """Get available Claude models"""
        return [
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307"
        ]
    
    def estimate_cost(self, request: AIRequest) -> float:
        """Estimate cost for the request"""
        # Rough estimation based on prompt length and expected response
        estimated_input_tokens = len(request.prompt.split()) * 1.3
        estimated_output_tokens = request.max_tokens or 1000
        
        model = self._get_best_model_for_task(request.task_type)
        
        # Cost per 1M tokens (approximate)
        if "opus" in model:
            input_cost_per_1m = 15.0
            output_cost_per_1m = 75.0
        elif "sonnet" in model:
            input_cost_per_1m = 3.0
            output_cost_per_1m = 15.0
        else:  # haiku
            input_cost_per_1m = 0.25
            output_cost_per_1m = 1.25
        
        total_cost = (
            (estimated_input_tokens * input_cost_per_1m / 1_000_000) +
            (estimated_output_tokens * output_cost_per_1m / 1_000_000)
        )
        
        return round(total_cost, 4)
    
    def _get_best_model_for_task(self, task_type: TaskType) -> str:
        """Select the best model for the given task type"""
        if task_type in [TaskType.ANALYSIS, TaskType.PLANNING, TaskType.REVIEW]:
            return "claude-3-opus-20240229"  # Best reasoning for complex tasks
        elif task_type == TaskType.CODE_GENERATION:
            return "claude-3-sonnet-20240229"  # Good balance for code generation
        elif task_type == TaskType.OPTIMIZATION:
            return "claude-3-sonnet-20240229"  # Good for optimization
        else:
            return "claude-3-haiku-20240307"  # Cost-effective for simpler tasks
    
    def _calculate_cost(self, input_tokens: int, output_tokens: int, model: str) -> float:
        """Calculate actual cost based on token usage"""
        if "opus" in model:
            input_cost = input_tokens * 15.0 / 1_000_000
            output_cost = output_tokens * 75.0 / 1_000_000
        elif "sonnet" in model:
            input_cost = input_tokens * 3.0 / 1_000_000
            output_cost = output_tokens * 15.0 / 1_000_000
        else:  # haiku
            input_cost = input_tokens * 0.25 / 1_000_000
            output_cost = output_tokens * 1.25 / 1_000_000
        
        return round(input_cost + output_cost, 4)
    
    def is_suitable_for_task(self, task_type: TaskType) -> bool:
        """Claude is excellent for analysis, planning, and code review"""
        return task_type in [
            TaskType.ANALYSIS,
            TaskType.PLANNING,
            TaskType.REVIEW,
            TaskType.OPTIMIZATION,
            TaskType.CODE_GENERATION
        ]
