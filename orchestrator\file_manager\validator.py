"""
Code validation and quality checking for the AI Coding Orchestrator.
"""

import ast
import re
import os
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
import time

@dataclass
class ValidationIssue:
    """Represents a validation issue found in code."""
    severity: str  # error, warning, info
    message: str
    line_number: Optional[int] = None
    column: Optional[int] = None
    code: Optional[str] = None
    suggestion: Optional[str] = None

@dataclass
class ValidationResult:
    """Result of code validation."""
    file_path: str
    is_valid: bool
    issues: List[ValidationIssue]
    score: float  # 0.0 to 1.0
    metadata: Dict[str, Any]

class CodeValidator:
    """Validates code quality and provides suggestions for improvement."""
    
    def __init__(self):
        # Language-specific validators
        self.validators = {
            '.py': self._validate_python,
            '.js': self._validate_javascript,
            '.jsx': self._validate_javascript,
            '.ts': self._validate_typescript,
            '.tsx': self._validate_typescript,
            '.html': self._validate_html,
            '.css': self._validate_css,
            '.java': self._validate_java,
            '.cpp': self._validate_cpp,
            '.c': self._validate_c,
            '.go': self._validate_go,
            '.rs': self._validate_rust
        }
        
        # Common validation rules
        self.common_rules = {
            'max_line_length': 120,
            'min_function_length': 3,
            'max_function_length': 50,
            'require_docstrings': True,
            'require_type_hints': False  # Language-specific
        }
    
    async def validate_file(self, file_path: str) -> ValidationResult:
        """Validate a single file."""
        try:
            file_path_obj = Path(file_path)
            
            if not file_path_obj.exists():
                return ValidationResult(
                    file_path=file_path,
                    is_valid=False,
                    issues=[ValidationIssue("error", "File does not exist")],
                    score=0.0,
                    metadata={"error": "File not found"}
                )
            
            # Get file extension
            file_ext = file_path_obj.suffix.lower()
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Validate based on file type
            if file_ext in self.validators:
                validator_func = self.validators[file_ext]
                issues = await validator_func(content, file_path)
            else:
                # Generic validation for unknown file types
                issues = await self._validate_generic(content, file_path)
            
            # Calculate validation score
            score = self._calculate_score(issues, content)
            
            # Determine if file is valid
            is_valid = score >= 0.7 and not any(issue.severity == "error" for issue in issues)
            
            return ValidationResult(
                file_path=file_path,
                is_valid=is_valid,
                issues=issues,
                score=score,
                metadata={
                    "file_size": len(content),
                    "lines": len(content.splitlines()),
                    "extension": file_ext
                }
            )
            
        except Exception as e:
            return ValidationResult(
                file_path=file_path,
                is_valid=False,
                issues=[ValidationIssue("error", f"Validation error: {e}")],
                score=0.0,
                metadata={"error": str(e)}
            )
    
    async def validate_directory(self, directory_path: str, recursive: bool = True) -> Dict[str, ValidationResult]:
        """Validate all code files in a directory."""
        results = {}
        directory = Path(directory_path)
        
        if not directory.exists() or not directory.is_dir():
            return results
        
        # Get all files to validate
        if recursive:
            files = directory.rglob("*")
        else:
            files = directory.glob("*")
        
        # Filter for code files
        code_files = [f for f in files if f.is_file() and f.suffix.lower() in self.validators]
        
        # Validate each file
        for file_path in code_files:
            result = await self.validate_file(str(file_path))
            results[str(file_path)] = result
        
        return results
    
    async def _validate_python(self, content: str, file_path: str) -> List[ValidationIssue]:
        """Validate Python code."""
        issues = []
        
        try:
            # Parse AST
            tree = ast.parse(content)
            
            # Basic syntax validation
            if not tree.body:
                issues.append(ValidationIssue("warning", "File is empty", suggestion="Add some code content"))
            
            # Check line length
            lines = content.splitlines()
            for i, line in enumerate(lines, 1):
                if len(line) > self.common_rules['max_line_length']:
                    issues.append(ValidationIssue(
                        "warning",
                        f"Line too long ({len(line)} > {self.common_rules['max_line_length']} characters)",
                        line_number=i,
                        suggestion="Break long lines or use line continuation"
                    ))
            
            # Check for common Python issues
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    # Function length check
                    func_lines = len(node.body)
                    if func_lines > self.common_rules['max_function_length']:
                        issues.append(ValidationIssue(
                            "warning",
                            f"Function '{node.name}' is too long ({func_lines} lines)",
                            line_number=node.lineno,
                            suggestion="Consider breaking into smaller functions"
                        ))
                    
                    # Docstring check
                    if self.common_rules['require_docstrings']:
                        if not node.body or not isinstance(node.body[0], ast.Expr) or not isinstance(node.body[0].value, ast.Str):
                            issues.append(ValidationIssue(
                                "info",
                                f"Function '{node.name}' missing docstring",
                                line_number=node.lineno,
                                suggestion="Add a docstring describing the function's purpose"
                            ))
                
                elif isinstance(node, ast.ClassDef):
                    # Class validation
                    if not node.body:
                        issues.append(ValidationIssue(
                            "warning",
                            f"Class '{node.name}' is empty",
                            line_number=node.lineno,
                            suggestion="Add methods or attributes to the class"
                        ))
                
                elif isinstance(node, ast.Import):
                    # Import validation
                    for alias in node.names:
                        if alias.name.startswith('_'):
                            issues.append(ValidationIssue(
                                "info",
                                f"Importing private module '{alias.name}'",
                                line_number=node.lineno,
                                suggestion="Consider if this import is necessary"
                            ))
            
            # Check for unused imports (basic check)
            import_names = set()
            used_names = set()
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        import_names.add(alias.asname or alias.name)
                elif isinstance(node, ast.ImportFrom):
                    for alias in node.names:
                        import_names.add(alias.asname or alias.name)
                elif isinstance(node, ast.Name):
                    used_names.add(node.id)
            
            unused_imports = import_names - used_names
            for unused in unused_imports:
                issues.append(ValidationIssue(
                    "warning",
                    f"Unused import '{unused}'",
                    suggestion="Remove unused import or use the imported item"
                ))
            
        except SyntaxError as e:
            issues.append(ValidationIssue(
                "error",
                f"Syntax error: {e.msg}",
                line_number=e.lineno,
                column=e.offset,
                suggestion="Fix the syntax error to make the code valid Python"
            ))
        except Exception as e:
            issues.append(ValidationIssue(
                "error",
                f"AST parsing error: {e}",
                suggestion="Check for malformed Python code"
            ))
        
        return issues
    
    async def _validate_javascript(self, content: str, file_path: str) -> List[ValidationIssue]:
        """Validate JavaScript code."""
        issues = []
        
        lines = content.splitlines()
        
        # Basic JavaScript validation
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Check line length
            if len(line) > self.common_rules['max_line_length']:
                issues.append(ValidationIssue(
                    "warning",
                    f"Line too long ({len(line)} > {self.common_rules['max_line_length']} characters)",
                    line_number=i,
                    suggestion="Break long lines or use line continuation"
                ))
            
            # Check for common JavaScript issues
            if line and not line.startswith(('//', '/*', '*', '*/')):
                # Check for missing semicolons (basic check)
                if (line.endswith((';', '{', '}', ':', ',')) or 
                    line.startswith(('if', 'for', 'while', 'switch', 'try', 'catch', 'finally', 'function', 'class'))):
                    continue
                elif not line.endswith(';'):
                    # This is a basic check - in practice, many valid JS lines don't end with semicolons
                    pass
        
        # Check for basic structure
        if not content.strip():
            issues.append(ValidationIssue("warning", "File is empty", suggestion="Add some code content"))
        
        return issues
    
    async def _validate_typescript(self, content: str, file_path: str) -> List[ValidationIssue]:
        """Validate TypeScript code."""
        # TypeScript validation is similar to JavaScript but with additional type checks
        issues = await self._validate_javascript(content, file_path)
        
        # Add TypeScript-specific validations
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Check for any type usage
            if ': any' in line:
                issues.append(ValidationIssue(
                    "warning",
                    "Usage of 'any' type",
                    line_number=i,
                    suggestion="Consider using more specific types instead of 'any'"
                ))
            
            # Check for missing type annotations in function parameters
            if line.startswith('function ') and '(' in line and ')' in line:
                if not re.search(r':\s*\w+', line):
                    issues.append(ValidationIssue(
                        "info",
                        "Function parameters missing type annotations",
                        line_number=i,
                        suggestion="Add type annotations to function parameters"
                    ))
        
        return issues
    
    async def _validate_html(self, content: str, file_path: str) -> List[ValidationIssue]:
        """Validate HTML code."""
        issues = []
        
        # Basic HTML validation
        if not content.strip():
            issues.append(ValidationIssue("warning", "File is empty", suggestion="Add HTML content"))
            return issues
        
        # Check for basic HTML structure
        if '<!DOCTYPE html>' not in content and '<html' not in content:
            issues.append(ValidationIssue(
                "info",
                "Missing proper HTML structure",
                suggestion="Consider adding <!DOCTYPE html> and <html> tags"
            ))
        
        # Check for title tag
        if '<title>' not in content:
            issues.append(ValidationIssue(
                "info",
                "Missing <title> tag",
                suggestion="Add a title tag for better SEO and accessibility"
            ))
        
        # Check for meta tags
        if '<meta' not in content:
            issues.append(ValidationIssue(
                "info",
                "Missing meta tags",
                suggestion="Add meta tags for charset, viewport, and description"
            ))
        
        # Check line length
        lines = content.splitlines()
        for i, line in enumerate(lines, 1):
            if len(line) > self.common_rules['max_line_length']:
                issues.append(ValidationIssue(
                    "warning",
                    f"Line too long ({len(line)} > {self.common_rules['max_line_length']} characters)",
                    line_number=i,
                    suggestion="Break long lines for better readability"
                ))
        
        return issues
    
    async def _validate_css(self, content: str, file_path: str) -> List[ValidationIssue]:
        """Validate CSS code."""
        issues = []
        
        if not content.strip():
            issues.append(ValidationIssue("warning", "File is empty", suggestion="Add CSS content"))
            return issues
        
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Check line length
            if len(line) > self.common_rules['max_line_length']:
                issues.append(ValidationIssue(
                    "warning",
                    f"Line too long ({len(line)} > {self.common_rules['max_line_length']} characters)",
                    line_number=i,
                    suggestion="Break long lines for better readability"
                ))
            
            # Check for missing semicolons
            if ':' in line and not line.endswith(';') and not line.endswith('}'):
                if not line.startswith(('@', '/*', '*/')):
                    issues.append(ValidationIssue(
                        "info",
                        "Missing semicolon",
                        line_number=i,
                        suggestion="Add semicolon at the end of CSS property"
                    ))
        
        return issues
    
    async def _validate_java(self, content: str, file_path: str) -> List[ValidationIssue]:
        """Validate Java code."""
        issues = []
        
        if not content.strip():
            issues.append(ValidationIssue("warning", "File is empty", suggestion="Add Java code"))
            return issues
        
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Check line length
            if len(line) > self.common_rules['max_line_length']:
                issues.append(ValidationIssue(
                    "warning",
                    f"Line too long ({len(line)} > {self.common_rules['max_line_length']} characters)",
                    line_number=i,
                    suggestion="Break long lines for better readability"
                ))
        
        return issues
    
    async def _validate_cpp(self, content: str, file_path: str) -> List[ValidationIssue]:
        """Validate C++ code."""
        return await self._validate_c(content, file_path)
    
    async def _validate_c(self, content: str, file_path: str) -> List[ValidationIssue]:
        """Validate C code."""
        issues = []
        
        if not content.strip():
            issues.append(ValidationIssue("warning", "File is empty", suggestion="Add C code"))
            return issues
        
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Check line length
            if len(line) > self.common_rules['max_line_length']:
                issues.append(ValidationIssue(
                    "warning",
                    f"Line too long ({len(line)} > {self.common_rules['max_line_length']} characters)",
                    line_number=i,
                    suggestion="Break long lines for better readability"
                ))
        
        return issues
    
    async def _validate_go(self, content: str, file_path: str) -> List[ValidationIssue]:
        """Validate Go code."""
        issues = []
        
        if not content.strip():
            issues.append(ValidationIssue("warning", "File is empty", suggestion="Add Go code"))
            return issues
        
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Check line length
            if len(line) > self.common_rules['max_line_length']:
                issues.append(ValidationIssue(
                    "warning",
                    f"Line too long ({len(line)} > {self.common_rules['max_line_length']} characters)",
                    line_number=i,
                    suggestion="Break long lines for better readability"
                ))
        
        return issues
    
    async def _validate_rust(self, content: str, file_path: str) -> List[ValidationIssue]:
        """Validate Rust code."""
        issues = []
        
        if not content.strip():
            issues.append(ValidationIssue("warning", "File is empty", suggestion="Add Rust code"))
            return issues
        
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Check line length
            if len(line) > self.common_rules['max_line_length']:
                issues.append(ValidationIssue(
                    "warning",
                    f"Line too long ({len(line)} > {self.common_rules['max_line_length']} characters)",
                    line_number=i,
                    suggestion="Break long lines for better readability"
                ))
        
        return issues
    
    async def _validate_generic(self, content: str, file_path: str) -> List[ValidationIssue]:
        """Generic validation for unknown file types."""
        issues = []
        
        if not content.strip():
            issues.append(ValidationIssue("warning", "File is empty", suggestion="Add content to the file"))
            return issues
        
        # Basic line length check
        lines = content.splitlines()
        for i, line in enumerate(lines, 1):
            if len(line) > self.common_rules['max_line_length']:
                issues.append(ValidationIssue(
                    "info",
                    f"Line too long ({len(line)} > {self.common_rules['max_line_length']} characters)",
                    line_number=i,
                    suggestion="Consider breaking long lines for better readability"
                ))
        
        return issues
    
    def _calculate_score(self, issues: List[ValidationIssue], content: str) -> float:
        """Calculate a validation score from 0.0 to 1.0."""
        if not content.strip():
            return 0.0
        
        # Base score
        base_score = 1.0
        
        # Deduct points for issues
        for issue in issues:
            if issue.severity == "error":
                base_score -= 0.3
            elif issue.severity == "warning":
                base_score -= 0.1
            elif issue.severity == "info":
                base_score -= 0.05
        
        # Ensure score is between 0.0 and 1.0
        return max(0.0, min(1.0, base_score))
    
    def get_validation_summary(self, results: Dict[str, ValidationResult]) -> Dict[str, Any]:
        """Get a summary of validation results."""
        total_files = len(results)
        valid_files = sum(1 for r in results.values() if r.is_valid)
        total_issues = sum(len(r.issues) for r in results.values())
        
        # Count issues by severity
        severity_counts = {"error": 0, "warning": 0, "info": 0}
        for result in results.values():
            for issue in result.issues:
                severity_counts[issue.severity] += 1
        
        # Calculate average score
        avg_score = sum(r.score for r in results.values()) / total_files if total_files > 0 else 0.0
        
        return {
            "total_files": total_files,
            "valid_files": valid_files,
            "invalid_files": total_files - valid_files,
            "total_issues": total_issues,
            "severity_distribution": severity_counts,
            "average_score": avg_score,
            "validation_rate": valid_files / total_files if total_files > 0 else 0.0
        }
    
    def export_validation_report(self, results: Dict[str, ValidationResult], output_file: str) -> None:
        """Export validation results to a report file."""
        try:
            summary = self.get_validation_summary(results)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("# Code Validation Report\n\n")
                f.write(f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # Summary
                f.write("## Summary\n\n")
                f.write(f"- **Total Files**: {summary['total_files']}\n")
                f.write(f"- **Valid Files**: {summary['valid_files']}\n")
                f.write(f"- **Invalid Files**: {summary['invalid_files']}\n")
                f.write(f"- **Total Issues**: {summary['total_issues']}\n")
                f.write(f"- **Average Score**: {summary['average_score']:.2f}\n")
                f.write(f"- **Validation Rate**: {summary['validation_rate']:.1%}\n\n")
                
                # Issues by severity
                f.write("## Issues by Severity\n\n")
                for severity, count in summary['severity_distribution'].items():
                    f.write(f"- **{severity.title()}**: {count}\n")
                f.write("\n")
                
                # Detailed results
                f.write("## Detailed Results\n\n")
                for file_path, result in results.items():
                    f.write(f"### {file_path}\n\n")
                    f.write(f"- **Status**: {'✅ Valid' if result.is_valid else '❌ Invalid'}\n")
                    f.write(f"- **Score**: {result.score:.2f}\n")
                    f.write(f"- **Issues**: {len(result.issues)}\n\n")
                    
                    if result.issues:
                        f.write("**Issues Found:**\n\n")
                        for issue in result.issues:
                            location = f" (Line {issue.line_number})" if issue.line_number else ""
                            f.write(f"- **{issue.severity.title()}**: {issue.message}{location}\n")
                            if issue.suggestion:
                                f.write(f"  - Suggestion: {issue.suggestion}\n")
                        f.write("\n")
            
            print(f"📄 Validation report exported to: {output_file}")
            
        except Exception as e:
            print(f"Failed to export validation report: {e}")
    
    def __str__(self) -> str:
        return f"CodeValidator(supported_languages={len(self.validators)})"
    
    def __repr__(self) -> str:
        return self.__str__()
