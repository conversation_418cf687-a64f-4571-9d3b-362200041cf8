"""
Configuration management for the AI Coding Orchestrator.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class AIProviderConfig:
    """Configuration for AI providers."""
    openai_api_key: Optional[str] = None
    google_ai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    preferred_provider: str = "auto"
    max_retries: int = 3
    timeout: int = 30

@dataclass
class LearningConfig:
    """Configuration for the learning system."""
    database_path: str = "data/patterns.db"
    enable_learning: bool = True
    pattern_threshold: float = 0.8
    max_patterns: int = 1000

@dataclass
class CursorConfig:
    """Configuration for Cursor integration."""
    instruction_file_path: str = "cursor_instructions.md"
    clipboard_integration: bool = True
    file_monitoring: bool = True
    auto_validation: bool = True

@dataclass
class OrchestratorConfig:
    """Main configuration for the orchestrator."""
    ai_providers: AIProviderConfig
    learning: LearningConfig
    cursor: CursorConfig
    log_level: str = "INFO"
    max_concurrent_tasks: int = 3
    default_tech_stack: Dict[str, str] = None
    verbose: bool = False
    # UX
    stream_code_previews: bool = False
    # Rate limiting behavior
    wait_on_rate_limit: bool = True
    max_rate_limit_wait_seconds: int = 60

class ConfigManager:
    """Manages configuration loading and validation."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config.yaml"
        self.config = self._load_config()
    
    def _load_config(self) -> OrchestratorConfig:
        """Load configuration from file and environment variables."""
        # Load from YAML if exists
        yaml_config = self._load_yaml_config()
        
        # Create AI provider config
        ai_config = AIProviderConfig(
            openai_api_key=os.getenv("OPENAI_API_KEY") or yaml_config.get("ai_providers", {}).get("openai_api_key"),
            google_ai_api_key=os.getenv("GOOGLE_AI_API_KEY") or yaml_config.get("ai_providers", {}).get("google_ai_api_key"),
            anthropic_api_key=os.getenv("ANTHROPIC_API_KEY") or yaml_config.get("ai_providers", {}).get("anthropic_api_key"),
            preferred_provider=yaml_config.get("ai_providers", {}).get("preferred_provider", "auto"),
            max_retries=yaml_config.get("ai_providers", {}).get("max_retries", 3),
            timeout=yaml_config.get("ai_providers", {}).get("timeout", 30)
        )
        
        # Create learning config
        learning_config = LearningConfig(
            database_path=yaml_config.get("learning", {}).get("database_path", "data/patterns.db"),
            enable_learning=yaml_config.get("learning", {}).get("enable_learning", True),
            pattern_threshold=yaml_config.get("learning", {}).get("pattern_threshold", 0.8),
            max_patterns=yaml_config.get("learning", {}).get("max_patterns", 1000)
        )
        
        # Create Cursor config
        cursor_config = CursorConfig(
            instruction_file_path=yaml_config.get("cursor", {}).get("instruction_file_path", "cursor_instructions.md"),
            clipboard_integration=yaml_config.get("cursor", {}).get("clipboard_integration", True),
            file_monitoring=yaml_config.get("cursor", {}).get("file_monitoring", True),
            auto_validation=yaml_config.get("cursor", {}).get("auto_validation", True)
        )
        
        # Create main config
        config = OrchestratorConfig(
            ai_providers=ai_config,
            learning=learning_config,
            cursor=cursor_config,
            log_level=yaml_config.get("log_level", "INFO"),
            max_concurrent_tasks=yaml_config.get("max_concurrent_tasks", 3),
            default_tech_stack=yaml_config.get("default_tech_stack", {}),
            verbose=yaml_config.get("verbose", False),
            stream_code_previews=yaml_config.get("stream_code_previews", False),
            wait_on_rate_limit=yaml_config.get("wait_on_rate_limit", True),
            max_rate_limit_wait_seconds=yaml_config.get("max_rate_limit_wait_seconds", 60)
        )
        
        return config
    
    def _load_yaml_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r') as f:
                    return yaml.safe_load(f) or {}
        except Exception as e:
            print(f"Warning: Could not load config file: {e}")
        
        return {}
    
    def get_config(self) -> OrchestratorConfig:
        """Get the current configuration."""
        return self.config
    
    def save_config(self) -> None:
        """Persist the current configuration to YAML file."""
        try:
            config_dict = {
                "ai_providers": {
                    "openai_api_key": self.config.ai_providers.openai_api_key,
                    "google_ai_api_key": self.config.ai_providers.google_ai_api_key,
                    "anthropic_api_key": self.config.ai_providers.anthropic_api_key,
                    "preferred_provider": self.config.ai_providers.preferred_provider,
                    "max_retries": self.config.ai_providers.max_retries,
                    "timeout": self.config.ai_providers.timeout,
                },
                "learning": {
                    "database_path": self.config.learning.database_path,
                    "enable_learning": self.config.learning.enable_learning,
                    "pattern_threshold": self.config.learning.pattern_threshold,
                    "max_patterns": self.config.learning.max_patterns,
                },
                "cursor": {
                    "instruction_file_path": self.config.cursor.instruction_file_path,
                    "clipboard_integration": self.config.cursor.clipboard_integration,
                    "file_monitoring": self.config.cursor.file_monitoring,
                    "auto_validation": self.config.cursor.auto_validation,
                },
                "log_level": self.config.log_level,
                "max_concurrent_tasks": self.config.max_concurrent_tasks,
                "default_tech_stack": self.config.default_tech_stack or {},
                "verbose": self.config.verbose,
                "stream_code_previews": self.config.stream_code_previews,
                "wait_on_rate_limit": self.config.wait_on_rate_limit,
                "max_rate_limit_wait_seconds": self.config.max_rate_limit_wait_seconds,
            }
            with open(self.config_path, 'w') as f:
                yaml.dump(config_dict, f, default_flow_style=False, indent=2)
        except Exception as e:
            print(f"Error saving config file: {e}")

    def set_preferred_provider(self, provider_name: str) -> None:
        """Set and persist the preferred AI provider."""
        self.config.ai_providers.preferred_provider = provider_name
        self.save_config()

    def reset_to_default(self) -> None:
        """Reset configuration to default values and reload."""
        self.create_default_config()
        self.config = self._load_config()

    def validate_config(self) -> bool:
        """Validate the current configuration."""
        config = self.config
        
        # Check if at least one AI provider is configured
        if not any([
            config.ai_providers.openai_api_key,
            config.ai_providers.google_ai_api_key,
            config.ai_providers.anthropic_api_key
        ]):
            print("Warning: No AI provider API keys configured!")
            return False
        
        # Ensure data directory exists
        data_dir = Path(config.learning.database_path).parent
        data_dir.mkdir(parents=True, exist_ok=True)
        
        return True
    
    def create_default_config(self) -> None:
        """Create a default configuration file."""
        default_config = {
            "ai_providers": {
                "preferred_provider": "auto",
                "max_retries": 3,
                "timeout": 30
            },
            "learning": {
                "database_path": "data/patterns.db",
                "enable_learning": True,
                "pattern_threshold": 0.8,
                "max_patterns": 1000
            },
            "cursor": {
                "instruction_file_path": "cursor_instructions.md",
                "clipboard_integration": True,
                "file_monitoring": True,
                "auto_validation": True
            },
            "log_level": "INFO",
            "max_concurrent_tasks": 3,
            "default_tech_stack": {
                "frontend": "React",
                "backend": "FastAPI",
                "database": "PostgreSQL"
            }
        }
        
        try:
            with open(self.config_path, 'w') as f:
                yaml.dump(default_config, f, default_flow_style=False, indent=2)
            print(f"Created default configuration file: {self.config_path}")
        except Exception as e:
            print(f"Error creating config file: {e}")

# Global config instance
config_manager = ConfigManager()
