#!/usr/bin/env python3
"""
AI Coding Orchestrator - Comprehensive CLI Interface
Provides full control over all system settings and functions.
"""

import asyncio
import sys
import os
import json
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
import argparse
import subprocess
import shutil
import zipfile
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Ensure UTF-8 output for Windows consoles to avoid emoji/Unicode errors
try:
    sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    sys.stderr.reconfigure(encoding='utf-8', errors='replace')
except Exception:
    pass

from orchestrator.orchestrator_main import OrchestratorMain
from orchestrator.config import config_manager
from orchestrator.ai_providers.provider_manager import AIProviderManager

class OrchestratorCLI:
    """Comprehensive CLI interface for the AI Coding Orchestrator."""
    
    def __init__(self):
        self.orchestrator = None
        self.is_initialized = False
        
    async def initialize(self) -> bool:
        """Initialize the orchestrator."""
        try:
            self.orchestrator = OrchestratorMain()
            if await self.orchestrator.initialize():
                self.is_initialized = True
                return True
            return False
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            return False
    
    def show_banner(self):
        """Display the CLI banner."""
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    AI CODING ORCHESTRATOR                    ║
    ║                                                              ║
    ║  🚀 Intelligent development workflow management             ║
    ║  🔍 Deep request analysis and project planning              ║
    ║  🤖 Multi-AI provider support (OpenAI, Gemini, Claude)      ║
    ║  🔧 AI-optimized prompts for Cursor                         ║
    ║  📚 Continuous learning and improvement                     ║
    ║  📁 File monitoring and validation                          ║
    ║  🔄 Self-correction and error recovery                      ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
    
    def show_help(self):
        """Show comprehensive help."""
        print("""
📚 AI CODING ORCHESTRATOR CLI - COMPLETE COMMAND REFERENCE

🎯 PROJECT COMMANDS:
  create <request>     - Create a new project from natural language request
  analyze <request>    - Analyze a request without creating project
  plan <request>       - Generate project plan only
  projects             - List all created projects
  open-project <name>  - Open a project in your default editor
  delete-project <name> - Delete a project

🔧 SYSTEM COMMANDS:
  health               - Show system health and status
  status               - Show current system status
  config               - Show current configuration
  config-edit          - Edit configuration interactively
  config-reset         - Reset to default configuration
  config-validate      - Validate current configuration

🤖 AI PROVIDER COMMANDS:
  providers            - List AI providers and their status
  test-providers       - Test all AI provider connections
  provider-info <name> - Get detailed info about a provider
  switch-provider <name> - Switch to a different AI provider

📚 LEARNING & IMPROVEMENT:
  learning-stats       - Show learning system statistics
  learning-reset       - Reset learning data
  patterns             - Show learned patterns
  optimize-prompts     - Optimize prompts based on learning

📁 FILE MANAGEMENT:
  monitor              - Start file monitoring
  validate <path>      - Validate code files
  scan <name>          - Scan and analyze a project
  fix <name>           - AI-powered project fixing and completion

⚙️ ADVANCED COMMANDS:
  interactive          - Enter interactive mode
  debug               - Enable debug mode
  export <what>       - Export data (learning | session <id>)
  backup              - Create system backup
  restore <backup>    - Restore from backup
  update              - Check for updates
  verbose on|off      - Toggle verbose, step-by-step reasoning output

📊 MONITORING COMMANDS:
  logs                - Show system logs
  metrics             - Show performance metrics
  history             - Show command history
  sessions            - Show active sessions

💡 EXAMPLES:
  python cli.py create "React todo app with FastAPI backend"
  python cli.py health
  python cli.py interactive
  python cli.py scan-project MyProject
  python cli.py fix-project MyProject

🔍 HELP:
  help                - Show this help message
  help <command>      - Show help for specific command
        """)
    
    async def create_project(self, request: str):
        """Create a new project."""
        if not self.is_initialized:
            print("❌ System not initialized. Run 'init' first.")
            return
        
        print(f"🚀 Creating project: {request}")
        print("=" * 60)
        
        try:
            result = await self.orchestrator.orchestrate_request(request)
            if result.success:
                print(f"\n✅ Project created successfully!")
                structure = result.project_plan.get('project_structure') if isinstance(result.project_plan, dict) else None
                def _get_struct_field(obj, field, default="Unknown"):
                    if obj is None:
                        return default
                    if hasattr(obj, field):
                        return getattr(obj, field)
                    if isinstance(obj, dict):
                        return obj.get(field, default)
                    return default
                root_dir = _get_struct_field(structure, 'root_directory', 'Unknown')
                print(f"📁 Location: {root_dir}")
                print(f"⏱️  Time taken: {result.total_time:.2f} seconds")
                
                # Offer to scan and fix the project
                project_name = _get_struct_field(structure, 'project_name', 'Unknown')
                if project_name != 'Unknown':
                    print(f"\n🔍 Would you like to scan and complete the project '{project_name}'?")
                    print("   Run: python cli.py scan-project " + project_name)
            else:
                print(f"\n❌ Project creation failed: {result.metadata.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"\n❌ Error: {e}")
    
    async def scan_project(self, project_name: str):
        """Scan and analyze a project for completeness."""
        if not self.is_initialized:
            print("❌ System not initialized. Run 'init' first.")
            return
        
        project_path = Path(f"projects/{project_name}")
        if not project_path.exists():
            print(f"❌ Project '{project_name}' not found.")
            return
        
        print(f"🔍 Scanning project: {project_name}")
        print("=" * 60)
        
        # Analyze project structure
        files = self._get_project_files(project_path)
        issues = self._analyze_project_issues(project_path, files)
        
        print(f"📊 Project Analysis Results:")
        print(f"  Total Files: {len(files)}")
        print(f"  Issues Found: {len(issues)}")
        
        if issues:
            print(f"\n❌ Issues Found:")
            for issue in issues:
                print(f"  - {issue}")
            
            print(f"\n🔧 Would you like to fix these issues?")
            print(f"   Run: python cli.py fix-project {project_name}")
        else:
            print(f"\n✅ Project appears to be complete!")
    
    def _get_project_files(self, project_path: Path) -> List[Path]:
        """Get all files in a project."""
        files = []
        for root, dirs, filenames in os.walk(project_path):
            for filename in filenames:
                files.append(Path(root) / filename)
        return files
    
    def _analyze_project_issues(self, project_path: Path, files: List[Path]) -> List[str]:
        """Analyze project for common issues."""
        issues = []
        
        # Check for empty files
        for file_path in files:
            if file_path.stat().st_size < 50:  # Less than 50 bytes
                issues.append(f"File '{file_path.relative_to(project_path)}' is very small/empty")
        
        # Check for missing essential files
        essential_files = ['README.md', '.gitignore']
        for essential in essential_files:
            if not any(f.name == essential for f in files):
                issues.append(f"Missing essential file: {essential}")
        
        # Check for TODO comments indicating incomplete code
        for file_path in files:
            if file_path.suffix in ['.py', '.js', '.jsx', '.ts', '.tsx']:
                try:
                    content = file_path.read_text(encoding='utf-8')
                    if 'TODO:' in content or '# TODO:' in content:
                        issues.append(f"File '{file_path.relative_to(project_path)}' contains TODO comments")
                except:
                    pass
        
        return issues
    
    async def analyze_request(self, request: str):
        """Analyze a request without creating a project."""
        if not self.is_initialized:
            print("❌ System not initialized. Run 'init' first.")
            return
        print(f"🔍 Analyzing request only: {request}")
        analysis = await self.orchestrator.request_analyzer.analyze_request(request)
        self.orchestrator._display_analysis(analysis)

    async def plan_request(self, request: str):
        """Generate a project plan for a request only."""
        if not self.is_initialized:
            print("❌ System not initialized. Run 'init' first.")
            return
        print(f"📋 Planning project for: {request}")
        analysis = await self.orchestrator.request_analyzer.analyze_request(request)
        project_plan = await self.orchestrator.project_planner.plan_project(analysis)
        self.orchestrator._display_project_plan(project_plan)

    async def fix_project(self, project_name: str):
        """AI-powered project fixing and completion."""
        if not self.is_initialized:
            print("❌ System not initialized. Run 'init' first.")
            return
        
        print(f"🔧 Fixing project: {project_name}")
        print("=" * 60)
        
        # This would integrate with the AI to fix issues
        # For now, show what would be done
        print("🤖 AI Analysis and Fixing:")
        print("  1. Scanning all project files")
        print("  2. Identifying incomplete code")
        print("  3. Generating missing implementations")
        print("  4. Validating code quality")
        print("  5. Updating documentation")
        
        print(f"\n💡 This feature will be implemented in the next iteration.")
        print(f"   For now, you can manually review and complete the project.")
    
    async def open_project(self, project_name: str):
        """Open a project in the default editor/file explorer."""
        project_path = Path(f"projects/{project_name}").resolve()
        if not project_path.exists():
            print(f"❌ Project '{project_name}' not found.")
            return
        print(f"📂 Opening project: {project_path}")
        if sys.platform.startswith('win'):
            os.startfile(str(project_path))  # type: ignore[attr-defined]
        elif sys.platform == 'darwin':
            subprocess.run(['open', str(project_path)], check=False)
        else:
            subprocess.run(['xdg-open', str(project_path)], check=False)
    
    async def delete_project(self, project_name: str):
        """Delete a project directory."""
        project_path = Path(f"projects/{project_name}")
        if not project_path.exists():
            print(f"❌ Project '{project_name}' not found.")
            return
        shutil.rmtree(project_path)
        print(f"🗑️  Deleted project: {project_name}")
    
    async def show_health(self):
        """Show system health."""
        if not self.is_initialized:
            print("❌ System not initialized. Run 'init' first.")
            return
        
        health = self.orchestrator.get_system_health()
        
        print("🏥 SYSTEM HEALTH REPORT")
        print("=" * 60)
        print(f"Orchestrator Status: {health['orchestrator_status']}")
        print(f"AI Providers: {len(health['ai_providers'])} configured")
        
        print(f"\n🤖 AI Provider Status:")
        for provider, status in health['ai_providers'].items():
            status_icon = "✅" if status.get('available') else "❌"
            label = status.get('status', 'unknown')
            print(f"  {status_icon} {provider}: {label}")
        
        print(f"\n🧠 Learning System:")
        learning = health['learning_system']
        print(f"  Total Attempts: {learning['total_attempts']}")
        print(f"  Success Rate: {learning['success_rate']:.1%}")
        print(f"  Average Confidence: {learning['average_confidence']:.2f}")
        
        print(f"\n🔧 Prompt Optimization:")
        optimization = health['prompt_optimization']
        print(f"  Total Optimizations: {optimization['total_optimizations']}")
        print(f"  Success Rate: {optimization['success_rate']:.1%}")
        
        print(f"\n⚙️ Configuration:")
        config = health['configuration']
        print(f"  AI Providers Configured: {'✅' if config['ai_providers_configured'] else '❌'}")
        print(f"  Learning Enabled: {'✅' if config['learning_enabled'] else '❌'}")
        print(f"  Cursor Integration: {'✅' if config['cursor_integration'] else '❌'}")
    
    async def show_config(self):
        """Show current configuration."""
        config = config_manager.get_config()
        
        print("⚙️ CURRENT CONFIGURATION")
        print("=" * 60)
        
        print(f"AI Providers:")
        print(f"  OpenAI: {'✅ Configured' if config.ai_providers.openai_api_key else '❌ Not configured'}")
        print(f"  Gemini: {'✅ Configured' if config.ai_providers.google_ai_api_key else '❌ Not configured'}")
        print(f"  Claude: {'✅ Configured' if config.ai_providers.anthropic_api_key else '❌ Not configured'}")
        
        print(f"\nLearning System:")
        print(f"  Database Path: {config.learning.database_path}")
        print(f"  Enabled: {config.learning.enable_learning}")
        print(f"  Pattern Threshold: {config.learning.pattern_threshold}")
        
        print(f"\nCursor Integration:")
        print(f"  Instruction File: {config.cursor.instruction_file_path}")
        print(f"  Clipboard Integration: {config.cursor.clipboard_integration}")
        print(f"  File Monitoring: {config.cursor.file_monitoring}")
        
    async def config_edit(self):
        cfg = config_manager.get_config()
        print("Interactive config edit. Press Enter to keep current value.")
        openai_key = input(f"OpenAI API Key [{bool(cfg.ai_providers.openai_api_key)}]: ")
        if openai_key:
            cfg.ai_providers.openai_api_key = openai_key
        gemini_key = input(f"Google AI API Key [{bool(cfg.ai_providers.google_ai_api_key)}]: ")
        if gemini_key:
            cfg.ai_providers.google_ai_api_key = gemini_key
        claude_key = input(f"Anthropic API Key [{bool(cfg.ai_providers.anthropic_api_key)}]: ")
        if claude_key:
            cfg.ai_providers.anthropic_api_key = claude_key
        config_manager.save_config()
        print("✅ Configuration saved")
        
    async def config_reset(self):
        config_manager.reset_to_default()
        print("🔄 Configuration reset to defaults")
        
    async def config_validate(self):
        ok = config_manager.validate_config()
        print("✅ Config valid" if ok else "⚠️  Config has issues")
    
    async def list_providers(self):
        """List AI providers and status."""
        pm = self.orchestrator.ai_provider_manager
        health = pm.get_provider_health()
        print("🤖 Providers:")
        for name, info in health.items():
            status_icon = "✅" if info.get('available') else "❌"
            print(f"  {status_icon} {name} - {info.get('status', 'unknown')}")
        print(f"Preferred: {config_manager.get_config().ai_providers.preferred_provider}")
    
    async def test_providers(self):
        """Test connection to all providers."""
        pm = self.orchestrator.ai_provider_manager
        await pm.test_all_providers()
    
    async def provider_info(self, name: str):
        """Show details for a provider."""
        pm = self.orchestrator.ai_provider_manager
        provider = pm.providers.get(name)
        if not provider:
            print(f"❌ Provider '{name}' not found")
            return
        models = []
        try:
            models = provider.get_models()  # type: ignore[attr-defined]
        except Exception:
            pass
        health = pm.get_provider_health().get(name, {})
        print(f"Provider: {name}")
        print(f"  Available: {health.get('available', False)}")
        if models:
            print(f"  Models: {', '.join(models)}")
    
    async def switch_provider(self, name: str):
        """Switch preferred provider and persist to config."""
        config_manager.set_preferred_provider(name)
        print(f"🔁 Preferred provider set to: {name}")
    
    async def list_projects(self):
        """List all created projects."""
        projects_dir = Path("projects")
        if not projects_dir.exists():
            print("📁 No projects directory found.")
            return
        
        projects = [d for d in projects_dir.iterdir() if d.is_dir()]
        
        if not projects:
            print("📁 No projects found.")
            return
        
        print("📁 CREATED PROJECTS")
        print("=" * 60)
        
        for project in projects:
            # Count files
            file_count = len(list(project.rglob("*")))
            dir_count = len([d for d in project.rglob("*") if d.is_dir()])
            
            # Check if project has source code
            has_source = any(f.suffix in ['.py', '.js', '.jsx', '.ts', '.tsx', '.html', '.css'] 
                           for f in project.rglob("*") if f.is_file())
            
            status = "✅ Complete" if has_source else "⚠️  Incomplete"
            
            print(f"  {project.name}")
            print(f"    📊 Files: {file_count}, Directories: {dir_count}")
            print(f"    📍 Path: {project}")
            print(f"    🎯 Status: {status}")
            print()
    
    async def learning_stats(self):
        stats = self.orchestrator.learning_engine.get_learning_statistics()
        print("🧠 Learning Stats:")
        print(json.dumps(stats, indent=2))
    
    async def learning_reset(self):
        self.orchestrator.learning_engine.clear_learning_data()
        print("🧹 Cleared learning data")
    
    async def show_patterns(self):
        patterns = self.orchestrator.learning_engine.get_high_success_patterns()
        if not patterns:
            print("No high-success patterns yet")
            return
        print("🏆 High-success patterns:")
        for p in patterns[:10]:
            print(f"  {p.pattern_type} - success={p.success_rate:.2f} used={p.usage_count}")
    
    async def optimize_prompt_interactive(self):
        request = input("Enter a prompt to optimize: ")
        optimized = await self.orchestrator.prompt_optimizer.optimize_prompt(request)
        print("\nOptimized Prompt:\n")
        print(optimized.optimized_prompt)
    
    async def export_data(self, what: List[str]):
        exports = Path('exports'); exports.mkdir(exist_ok=True)
        if len(what) == 1 and what[0].lower() == 'learning':
            data = self.orchestrator.learning_engine.export_learning_data()
            out = exports / 'learning_export.json'
            out.write_text(json.dumps(data, indent=2), encoding='utf-8')
            print(f"📤 Exported learning data to {out}")
        elif len(what) == 2 and what[0].lower() == 'session':
            sid = what[1]
            data = self.orchestrator.export_session_data(sid)
            if not data:
                print("❌ Session not found")
                return
            out = exports / f"session_{sid}.json"
            out.write_text(json.dumps(data, indent=2), encoding='utf-8')
            print(f"📤 Exported session data to {out}")
        else:
            print("Usage: export learning | export session <id>")
    
    async def do_backup(self):
        ts = datetime.now().strftime('%Y%m%d_%H%M%S')
        exports = Path('exports'); exports.mkdir(exist_ok=True)
        backup_file = exports / f"backup_{ts}.zip"
        with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as z:
            for path in [Path('orchestrator'), Path('data'), Path('projects')]:
                if path.exists():
                    for p in path.rglob('*'):
                        if p.is_file():
                            z.write(p, p.as_posix())
            for p in [Path('config.yaml'), Path('README.md')]:
                if p.exists():
                    z.write(p, p.as_posix())
        print(f"🗄️  Backup created: {backup_file}")
    
    async def do_restore(self, backup_path: str):
        bp = Path(backup_path)
        if not bp.exists():
            print("❌ Backup file not found")
            return
        with zipfile.ZipFile(bp, 'r') as z:
            z.extractall(Path('.'))
        print("♻️  Restore completed")

    async def show_logs(self):
        """Show recent log files."""
        logs_dir = Path('logs')
        if logs_dir.exists():
            print("🗒️  Recent logs:")
            shown = 0
            for p in sorted(logs_dir.glob('**/*'), key=lambda x: x.stat().st_mtime, reverse=True):
                if p.is_file():
                    print(f"  {p} - {p.stat().st_size} bytes")
                    shown += 1
                    if shown >= 20:
                        break
        else:
            print("No logs directory")

    async def validate_file(self, path: str):
        """Validate a single file using the integrator."""
        if not self.is_initialized:
            print("❌ System not initialized. Run 'init' first.")
            return
        result = await self.orchestrator.cursor_integrator.validate_generated_code(path)
        print(json.dumps(result, indent=2))

    async def start_monitoring(self):
        """Start monitoring current directory for file changes and validate automatically."""
        if not self.is_initialized:
            await self.initialize()

        async def on_change(file_path: str, event_type: str):
            print(f"📣 File {event_type}: {file_path}")
            try:
                validation = await self.orchestrator.cursor_integrator.validate_generated_code(file_path)
                if validation and validation.get('status') != 'validation_disabled':
                    print(f"🔎 Validation for {file_path}:")
                    print(json.dumps(validation, indent=2))
            except Exception as e:
                print(f"Validation failed for {file_path}: {e}")

        await self.orchestrator.cursor_integrator.monitor_file_changes(on_change)
    
    async def interactive_mode(self):
        """Enter interactive main menu."""
        while True:
            print("\n" + "=" * 60)
            print("AI Coding Orchestrator - Main Menu")
            print("=" * 60)
            print("1) Guided orchestration (enhance → confirm → build)")
            print("2) Analyze request")
            print("3) Plan project")
            print("4) Projects (list/open/delete/scan/fix)")
            print("5) Providers (list/test/info/switch)")
            print("6) Learning (stats/patterns/reset)")
            print("7) Monitor & Validate")
            print("8) Status & Metrics (status/history/metrics/logs)")
            print("9) Config (show/edit/reset/validate)")
            print("10) Backup/Restore/Export")
            print("Q) Quit")
            choice = input("Select an option: ").strip().lower()
            if choice in ['q', 'quit', 'exit']:
                print("👋 Goodbye!")
                break
            try:
                if choice == '1':
                    await self.guided_orchestration()
                elif choice == '2':
                    req = input("Enter request to analyze: ").strip()
                    if req:
                        await self.analyze_request(req)
                elif choice == '3':
                    req = input("Enter request to plan: ").strip()
                    if req:
                        await self.plan_request(req)
                elif choice == '4':
                    await self._projects_menu()
                elif choice == '5':
                    await self._providers_menu()
                elif choice == '6':
                    await self._learning_menu()
                elif choice == '7':
                    await self._monitor_menu()
                elif choice == '8':
                    await self._status_menu()
                elif choice == '9':
                    await self._config_menu()
                elif choice == '10':
                    await self._backup_menu()
                else:
                    print("Invalid option.")
            except Exception as e:
                print(f"❌ Error: {e}")

    async def _projects_menu(self):
        while True:
            print("\nProjects Menu: 1) List 2) Open 3) Delete 4) Scan 5) Fix 0) Back")
            sub = input("Select: ").strip()
            if sub == '0':
                return
            if sub == '1':
                await self.list_projects()
            elif sub == '2':
                name = input("Project name to open: ")
                await self.open_project(name)
            elif sub == '3':
                name = input("Project name to delete: ")
                await self.delete_project(name)
            elif sub == '4':
                name = input("Project name to scan: ")
                await self.scan_project(name)
            elif sub == '5':
                name = input("Project name to fix: ")
                await self.fix_project(name)
            else:
                print("Invalid option")

    async def guided_orchestration(self):
        if not self.is_initialized:
            await self.initialize()
        # Enable verbose provider routing logs
        try:
            self.orchestrator.ai_provider_manager.set_verbose(True)
        except Exception:
            pass
        # Step 1: collect request
        user_request = input("\nDescribe your project: ").strip()
        if not user_request:
            print("No request provided.")
            return
        # Step 2: analyze
        print("\n🔍 Analyzing your request...")
        analysis = await self.orchestrator.request_analyzer.analyze_request(user_request)
        self.orchestrator._display_analysis(analysis)
        # Step 3: enhance prompt
        print("\n🧠 Enhancing your prompt for best results...")
        optimized = await self.orchestrator.prompt_optimizer.optimize_prompt(
            user_request,
            {
                "project_type": analysis.project_type,
                "technology_stack": analysis.technology_stack,
                "complexity": analysis.complexity_level,
            },
        )
        enhanced = optimized.optimized_prompt
        print("\n✨ Enhanced Prompt:\n")
        print(enhanced)
        # Allow edit/confirm
        choice = input("\nUse enhanced prompt? [Y/n/e=edit]: ").strip().lower()
        final_prompt = enhanced
        if choice == 'n':
            final_prompt = user_request
        elif choice == 'e':
            print("Enter your edited prompt. Finish with an empty line:")
            lines = []
            while True:
                line = input()
                if line == "":
                    break
                lines.append(line)
            final_prompt = "\n".join(lines) or enhanced
        # Step 4: only ask follow-ups if necessary
        clarifications = []
        if analysis.follow_up_questions:
            print("\n❓ A few clarifications will improve the plan. Press Enter to skip a question.")
            for q in analysis.follow_up_questions[:3]:
                ans = input(f"- {q}\n  Answer: ").strip()
                if ans:
                    clarifications.append((q, ans))
        clarifications_text = "\n".join([f"Q: {q}\nA: {a}" for q, a in clarifications]) if clarifications else ""
        # Step 5: confirm build
        go = input("\nProceed to build the project now? [Y/n]: ").strip().lower()
        if go == 'n':
            print("Canceled before build.")
            return
        # Step 6: build via orchestrator with context
        context = {
            "additional_context": clarifications_text,
            "enhanced_prompt": enhanced,
        }
        await self.create_project(final_prompt)

    async def _providers_menu(self):
        while True:
            print("\nProviders Menu: 1) List 2) Test 3) Info 4) Switch 0) Back")
            sub = input("Select: ").strip()
            if sub == '0':
                return
            if sub == '1':
                await self.list_providers()
            elif sub == '2':
                await self.test_providers()
            elif sub == '3':
                name = input("Provider name (openai|gemini|claude): ")
                await self.provider_info(name)
            elif sub == '4':
                name = input("Preferred provider (openai|gemini|claude|auto): ")
                await self.switch_provider(name)
            else:
                print("Invalid option")

    async def _learning_menu(self):
        while True:
            print("\nLearning Menu: 1) Stats 2) Patterns 3) Reset 0) Back")
            sub = input("Select: ").strip()
            if sub == '0':
                return
            if sub == '1':
                await self.learning_stats()
            elif sub == '2':
                await self.show_patterns()
            elif sub == '3':
                await self.learning_reset()
            else:
                print("Invalid option")

    async def _monitor_menu(self):
        while True:
            print("\nMonitor & Validate: 1) Start monitoring 2) Validate file 0) Back")
            sub = input("Select: ").strip()
            if sub == '0':
                return
            if sub == '1':
                await self.start_monitoring()
            elif sub == '2':
                path = input("File path to validate: ")
                await self.validate_file(path)
            else:
                print("Invalid option")

    async def _status_menu(self):
        while True:
            print("\nStatus & Metrics: 1) Status 2) History 3) Metrics 4) Logs 0) Back")
            sub = input("Select: ").strip()
            if sub == '0':
                return
            if sub == '1':
                status = self.orchestrator.get_session_status()
                print(json.dumps(status, indent=2))
            elif sub == '2':
                history = self.orchestrator.get_session_history()
                print(json.dumps(history, indent=2))
            elif sub == '3':
                await self.show_health()
            elif sub == '4':
                await self.show_logs()
            else:
                print("Invalid option")

    async def _config_menu(self):
        while True:
            print("\nConfig: 1) Show 2) Edit 3) Reset 4) Validate 0) Back")
            sub = input("Select: ").strip()
            if sub == '0':
                return
            if sub == '1':
                await self.show_config()
            elif sub == '2':
                await self.config_edit()
            elif sub == '3':
                await self.config_reset()
            elif sub == '4':
                await self.config_validate()
            else:
                print("Invalid option")

    async def _backup_menu(self):
        while True:
            print("\nBackup/Restore/Export: 1) Backup 2) Restore 3) Export Learning 4) Export Session 0) Back")
            sub = input("Select: ").strip()
            if sub == '0':
                return
            if sub == '1':
                await self.do_backup()
            elif sub == '2':
                path = input("Backup zip path: ")
                await self.do_restore(path)
            elif sub == '3':
                await self.export_data(['learning'])
            elif sub == '4':
                sid = input("Session ID: ")
                await self.export_data(['session', sid])
            else:
                print("Invalid option")
    
    async def run_command(self, args):
        """Run a CLI command."""
        if args.command == 'init':
            if await self.initialize():
                print("✅ System initialized successfully!")
            else:
                print("❌ System initialization failed!")
        
        elif args.command == 'create':
            if not self.is_initialized:
                await self.initialize()
            await self.create_project(args.request)
        
        elif args.command == 'analyze':
            if not self.is_initialized:
                await self.initialize()
            await self.analyze_request(args.request)
        
        elif args.command == 'plan':
            if not self.is_initialized:
                await self.initialize()
            await self.plan_request(args.request)
        
        elif args.command == 'health':
            if not self.is_initialized:
                await self.initialize()
            await self.show_health()
        
        elif args.command == 'config':
            await self.show_config()
        
        elif args.command == 'projects':
            await self.list_projects()
        
        elif args.command == 'scan':
            if not self.is_initialized:
                await self.initialize()
            await self.scan_project(args.project_name)
        
        elif args.command == 'fix':
            if not self.is_initialized:
                await self.initialize()
            await self.fix_project(args.project_name)
        
        elif args.command == 'interactive':
            if not self.is_initialized:
                await self.initialize()
            await self.interactive_mode()
        
        elif args.command == 'status':
            if not self.is_initialized:
                await self.initialize()
            status = self.orchestrator.get_session_status()
            print(json.dumps(status, indent=2))
        
        elif args.command in ['history', 'sessions']:
            if not self.is_initialized:
                await self.initialize()
            history = self.orchestrator.get_session_history()
            print(json.dumps(history, indent=2))
        
        elif args.command == 'providers':
            if not self.is_initialized:
                await self.initialize()
            await self.list_providers()
        
        elif args.command == 'test-providers':
            if not self.is_initialized:
                await self.initialize()
            await self.test_providers()
        
        elif args.command == 'provider-info':
            if not self.is_initialized:
                await self.initialize()
            await self.provider_info(args.name)
        
        elif args.command == 'switch-provider':
            await self.switch_provider(args.name)
        
        elif args.command == 'learning-stats':
            await self.learning_stats()
        
        elif args.command == 'learning-reset':
            await self.learning_reset()
        
        elif args.command == 'patterns':
            await self.show_patterns()
        
        elif args.command == 'optimize-prompts':
            if not self.is_initialized:
                await self.initialize()
            await self.optimize_prompt_interactive()
        
        elif args.command == 'metrics':
            await self.show_health()
        
        elif args.command == 'logs':
            await self.show_logs()
        
        elif args.command == 'backup':
            await self.do_backup()
        
        elif args.command == 'restore':
            await self.do_restore(args.backup_path)
        
        elif args.command == 'export':
            await self.export_data(args.what)
        
        elif args.command == 'verbose':
            # Toggle verbose mode in memory and persist to config
            if len(args.what) == 1 and args.what[0].lower() in ['on', 'off']:
                v = args.what[0].lower() == 'on'
                cfg = config_manager.get_config()
                cfg.verbose = v
                config_manager.save_config()
                try:
                    if self.is_initialized and self.orchestrator:
                        self.orchestrator.ai_provider_manager.set_verbose(v)
                except Exception:
                    pass
                print(f"🔊 Verbose mode: {'ON' if v else 'OFF'}")
            else:
                print("Usage: verbose on|off")
        
        elif args.command == 'open-project':
            await self.open_project(args.project_name)
        
        elif args.command == 'delete-project':
            await self.delete_project(args.project_name)
        
        elif args.command == 'monitor':
            if not self.is_initialized:
                await self.initialize()
            await self.start_monitoring()
        
        elif args.command == 'validate':
            if not self.is_initialized:
                await self.initialize()
            await self.validate_file(args.path)
        
        elif args.command == 'help':
            self.show_help()
        
        else:
            print(f"❓ Unknown command: {args.command}")
            self.show_help()

def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="AI Coding Orchestrator CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python cli.py init                    # Initialize the system
  python cli.py create "React app"      # Create a new project
  python cli.py health                  # Show system health
  python cli.py interactive             # Enter interactive mode
  python cli.py scan MyProject          # Scan a project for issues
  python cli.py fix MyProject           # Fix project issues with AI
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Init command
    subparsers.add_parser('init', help='Initialize the system')
    
    # Create command
    create_parser = subparsers.add_parser('create', help='Create a new project')
    create_parser.add_argument('request', help='Project request description')
    
    # Analyze command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze a request without creating a project')
    analyze_parser.add_argument('request', help='Request to analyze')
    
    # Plan command
    plan_parser = subparsers.add_parser('plan', help='Generate a project plan for a request')
    plan_parser.add_argument('request', help='Request to plan')
    
    # Health command
    subparsers.add_parser('health', help='Show system health')
    
    # Config command
    subparsers.add_parser('config', help='Show current configuration')
    
    # Projects command
    subparsers.add_parser('projects', help='List all projects')
    
    # Open/Delete project
    open_proj = subparsers.add_parser('open-project', help='Open a project in your default editor')
    open_proj.add_argument('project_name', help='Project name to open')
    del_proj = subparsers.add_parser('delete-project', help='Delete a project directory')
    del_proj.add_argument('project_name', help='Project name to delete')
    
    # Scan command
    scan_parser = subparsers.add_parser('scan', help='Scan a project for issues')
    scan_parser.add_argument('project_name', help='Name of the project to scan')
    
    # Fix command
    fix_parser = subparsers.add_parser('fix', help='Fix project issues with AI')
    fix_parser.add_argument('project_name', help='Name of the project to fix')
    
    # Interactive command
    subparsers.add_parser('interactive', help='Enter interactive mode')
    
    # Status/history/sessions
    subparsers.add_parser('status', help='Show current session status')
    subparsers.add_parser('history', help='Show session history')
    subparsers.add_parser('sessions', help='Alias for history')
    
    # Providers
    subparsers.add_parser('providers', help='List AI providers and their status')
    subparsers.add_parser('test-providers', help='Test all AI provider connections')
    pinfo = subparsers.add_parser('provider-info', help='Show detailed info about a provider')
    pinfo.add_argument('name', help='Provider name (openai|gemini|claude)')
    pswitch = subparsers.add_parser('switch-provider', help='Switch preferred provider')
    pswitch.add_argument('name', help='Provider name (openai|gemini|claude|auto)')
    
    # Learning
    subparsers.add_parser('learning-stats', help='Show learning system statistics')
    subparsers.add_parser('learning-reset', help='Reset learning data')
    subparsers.add_parser('patterns', help='Show high-success learned patterns')
    subparsers.add_parser('optimize-prompts', help='Optimize a prompt via interactive input')
    
    # Config management
    subparsers.add_parser('config-edit', help='Edit configuration interactively')
    subparsers.add_parser('config-reset', help='Reset configuration to default')
    subparsers.add_parser('config-validate', help='Validate current configuration')
    
    # Validate command
    val_parser = subparsers.add_parser('validate', help='Validate a file')
    val_parser.add_argument('path', help='Path to file to validate')
    
    # Monitor command
    subparsers.add_parser('monitor', help='Start file monitoring in current directory')
    
    # Metrics/logs
    subparsers.add_parser('metrics', help='Show performance metrics')
    subparsers.add_parser('logs', help='Show recent log files')
    
    # Backup/restore/export
    subparsers.add_parser('backup', help='Create system backup')
    restore = subparsers.add_parser('restore', help='Restore from backup zip')
    restore.add_argument('backup_path', help='Path to backup zip file')
    export_cmd = subparsers.add_parser('export', help='Export data (learning | session <id>)')
    export_cmd.add_argument('what', nargs='+', help='What to export: learning OR session <id>')
    # Verbose toggle
    verbose_cmd = subparsers.add_parser('verbose', help='Toggle verbose output (on|off)')
    verbose_cmd.add_argument('what', nargs='+', help='on|off')
    
    # Help command
    subparsers.add_parser('help', help='Show help')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Create CLI instance and run command
    cli = OrchestratorCLI()
    
    if args.command == 'init':
        asyncio.run(cli.initialize())
    else:
        asyncio.run(cli.run_command(args))

if __name__ == "__main__":
    main()
