"""
Abstract base class for AI providers.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

class TaskType(Enum):
    """Types of tasks for AI provider selection."""
    ANALYSIS = "analysis"
    CODE_GENERATION = "code_generation"
    OPTIMIZATION = "optimization"
    REVIEW = "review"
    PLANNING = "planning"

@dataclass
class AIResponse:
    """Standardized response from AI providers."""
    content: str
    provider: str
    model: str
    tokens_used: Optional[int] = None
    cost: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class AIRequest:
    """Standardized request to AI providers."""
    prompt: str
    task_type: TaskType
    context: Optional[Dict[str, Any]] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None

class AIProvider(ABC):
    """Abstract base class for AI providers."""
    
    def __init__(self, api_key: str, config: Dict[str, Any]):
        self.api_key = api_key
        self.config = config
        self.name = self.__class__.__name__
        self.is_available = False
        self._initialize()
    
    @abstractmethod
    def _initialize(self) -> None:
        """Initialize the provider connection."""
        pass
    
    @abstractmethod
    async def generate_response(self, request: AIRequest) -> AIResponse:
        """Generate a response from the AI provider."""
        pass
    
    @abstractmethod
    def get_models(self) -> List[str]:
        """Get available models for this provider."""
        pass
    
    @abstractmethod
    def estimate_cost(self, request: AIRequest) -> float:
        """Estimate the cost of a request."""
        pass
    
    def is_suitable_for_task(self, task_type: TaskType) -> bool:
        """Check if this provider is suitable for a specific task type."""
        # Default implementation - can be overridden by subclasses
        return True
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get the health status of the provider."""
        return {
            "name": self.name,
            "available": self.is_available,
            "status": "available" if self.is_available else "unavailable",
            "api_key_configured": bool(self.api_key),
            "models": self.get_models()
        }
    
    async def test_connection(self) -> bool:
        """Test the connection to the provider."""
        try:
            test_request = AIRequest(
                prompt="Hello, this is a test.",
                task_type=TaskType.ANALYSIS
            )
            await self.generate_response(test_request)
            self.is_available = True
            return True
        except Exception as e:
            self.is_available = False
            return False
    
    def __str__(self) -> str:
        return f"{self.name}(available={self.is_available})"
    
    def __repr__(self) -> str:
        return self.__str__()


class RateLimitError(Exception):
    """Raised when an AI provider indicates a rate limit/quota error.

    Attributes:
        retry_after_seconds: Suggested time to wait before retrying.
    """

    def __init__(self, message: str, retry_after_seconds: float = 30.0):
        super().__init__(message)
        self.retry_after_seconds = retry_after_seconds


class QuotaExceededError(Exception):
    """Raised when an AI provider quota is exceeded."""
    pass


class AuthenticationError(Exception):
    """Raised when authentication with AI provider fails."""
    pass


class ModelNotAvailableError(Exception):
    """Raised when requested model is not available."""
    pass


class ProviderTimeoutError(Exception):
    """Raised when provider request times out."""
    pass


class ProviderUnavailableError(Exception):
    """Raised when provider service is temporarily unavailable."""
    pass
