#!/usr/bin/env python3
"""
Comprehensive tests for the AI Coding Orchestrator.
"""

import asyncio
import sys
import unittest
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# Add the orchestrator package to the path
sys.path.insert(0, str(Path(__file__).parent))

from orchestrator.orchestrator_main import OrchestratorMain
from orchestrator.config import config_manager
from orchestrator.ai_providers.base_provider import TaskType, AIRequest, AIResponse
from orchestrator.analyzers.request_analyzer import RequestAnalyzer, RequestAnalysis
from orchestrator.prompt_engine.optimizer import PromptOptimizer, OptimizedPrompt
from orchestrator.prompt_engine.learning import LearningEngine

class TestOrchestrator(unittest.TestCase):
    """Test cases for the AI Coding Orchestrator."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Mock configuration
        self.mock_config = Mock()
        self.mock_config.ai_providers.openai_api_key = "test_key"
        self.mock_config.ai_providers.google_ai_api_key = "test_key"
        self.mock_config.ai_providers.anthropic_api_key = "test_key"
        self.mock_config.learning.enable_learning = True
        self.mock_config.cursor.file_monitoring = True
        
        # Mock AI provider manager
        self.mock_provider_manager = Mock()
        self.mock_provider_manager.test_all_providers.return_value = {"openai": True}
        self.mock_provider_manager.get_provider_stats.return_value = {}
        self.mock_provider_manager.get_provider_health.return_value = {}
        self.mock_provider_manager.get_recommended_provider.return_value = "openai"
        
        # Mock learning engine
        self.mock_learning_engine = Mock()
        self.mock_learning_engine.get_total_attempts.return_value = 0
        self.mock_learning_engine.get_success_rate.return_value = 0.0
        self.mock_learning_engine.get_average_confidence.return_value = 0.0
        self.mock_learning_engine.get_task_type_distribution.return_value = {}
        self.mock_learning_engine.get_learning_statistics.return_value = {}
    
    def test_config_manager(self):
        """Test configuration management."""
        # Test default config creation
        config_manager.create_default_config()
        
        # Test config validation
        self.assertTrue(config_manager.validate_config())
    
    def test_request_analyzer_initialization(self):
        """Test request analyzer initialization."""
        analyzer = RequestAnalyzer(self.mock_provider_manager)
        self.assertIsNotNone(analyzer)
        self.assertIsNotNone(analyzer.tech_patterns)
    
    def test_project_planner_initialization(self):
        """Test project planner initialization."""
        from orchestrator.analyzers.project_planner import ProjectPlanner
        planner = ProjectPlanner(self.mock_provider_manager)
        self.assertIsNotNone(planner)
        self.assertIsNotNone(planner.project_templates)
    
    def test_prompt_optimizer_initialization(self):
        """Test prompt optimizer initialization."""
        optimizer = PromptOptimizer(self.mock_provider_manager, self.mock_learning_engine)
        self.assertIsNotNone(optimizer)
        self.assertIsNotNone(optimizer.optimization_strategies)
    
    def test_learning_engine_initialization(self):
        """Test learning engine initialization."""
        engine = LearningEngine()
        self.assertIsNotNone(engine)
    
    def test_task_type_enum(self):
        """Test task type enumeration."""
        self.assertEqual(TaskType.ANALYSIS.value, "analysis")
        self.assertEqual(TaskType.CODE_GENERATION.value, "code_generation")
        self.assertEqual(TaskType.OPTIMIZATION.value, "optimization")
        self.assertEqual(TaskType.REVIEW.value, "review")
        self.assertEqual(TaskType.PLANNING.value, "planning")
    
    def test_ai_request_creation(self):
        """Test AI request creation."""
        request = AIRequest(
            prompt="Test prompt",
            task_type=TaskType.ANALYSIS,
            context={"test": "value"}
        )
        self.assertEqual(request.prompt, "Test prompt")
        self.assertEqual(request.task_type, TaskType.ANALYSIS)
        self.assertEqual(request.context["test"], "value")
    
    def test_ai_response_creation(self):
        """Test AI response creation."""
        response = AIResponse(
            content="Test response",
            provider="test_provider",
            model="test_model"
        )
        self.assertEqual(response.content, "Test response")
        self.assertEqual(response.provider, "test_provider")
        self.assertEqual(response.model, "test_model")
    
    def test_request_analysis_creation(self):
        """Test request analysis creation."""
        analysis = RequestAnalysis(
            original_request="Test request",
            identified_components=[],
            complexity_level="simple",
            estimated_time="1 hour",
            risk_factors=[],
            follow_up_questions=[],
            technology_stack={},
            project_type="test",
            requirements=[],
            constraints=[]
        )
        self.assertEqual(analysis.original_request, "Test request")
        self.assertEqual(analysis.complexity_level, "simple")
        self.assertEqual(analysis.estimated_time, "1 hour")
    
    def test_optimized_prompt_creation(self):
        """Test optimized prompt creation."""
        prompt = OptimizedPrompt(
            original_request="Test request",
            optimized_prompt="Optimized test prompt",
            context={},
            task_type="test",
            confidence_score=0.8,
            provider="test_provider",
            metadata={}
        )
        self.assertEqual(prompt.original_request, "Test request")
        self.assertEqual(prompt.optimized_prompt, "Optimized test prompt")
        self.assertEqual(prompt.confidence_score, 0.8)
    
    @patch('orchestrator.ai_providers.provider_manager.AIProviderManager')
    @patch('orchestrator.prompt_engine.learning.LearningEngine')
    @patch('orchestrator.analyzers.request_analyzer.RequestAnalyzer')
    @patch('orchestrator.analyzers.project_planner.ProjectPlanner')
    @patch('orchestrator.prompt_engine.optimizer.PromptOptimizer')
    @patch('orchestrator.file_manager.integrator.CursorIntegrator')
    def test_orchestrator_initialization(self, mock_integrator, mock_optimizer, 
                                       mock_planner, mock_analyzer, mock_learning, mock_provider):
        """Test orchestrator initialization."""
        # Mock the components
        mock_provider.return_value = self.mock_provider_manager
        mock_learning.return_value = self.mock_learning_engine
        mock_analyzer.return_value = Mock()
        mock_planner.return_value = Mock()
        mock_optimizer.return_value = Mock()
        mock_integrator.return_value = Mock()
        
        # Create orchestrator
        orchestrator = OrchestratorMain()
        self.assertIsNotNone(orchestrator)
    
    def test_pattern_analysis(self):
        """Test pattern-based analysis."""
        analyzer = RequestAnalyzer(self.mock_provider_manager)
        
        # Test technology identification
        request = "Build me a React app with FastAPI backend and PostgreSQL database"
        analysis = analyzer._pattern_analysis(request)
        
        self.assertIn("components", analysis)
        self.assertIn("technology_stack", analysis)
        self.assertIn("project_type", analysis)
        self.assertIn("complexity", analysis)
    
    def test_complexity_estimation(self):
        """Test complexity estimation."""
        analyzer = RequestAnalyzer(self.mock_provider_manager)
        
        # Test simple request
        simple_request = "Create a hello world function"
        complexity = analyzer._estimate_complexity(simple_request, 1)
        self.assertIn(complexity, ["simple", "medium", "complex", "enterprise"])
        
        # Test complex request
        complex_request = "Build a full-stack e-commerce platform with authentication, payment processing, inventory management, and admin dashboard"
        complexity = analyzer._estimate_complexity(complex_request, 5)
        self.assertIn(complexity, ["simple", "medium", "complex", "enterprise"])
    
    def test_project_type_identification(self):
        """Test project type identification."""
        analyzer = RequestAnalyzer(self.mock_provider_manager)
        
        # Test different project types
        self.assertEqual(analyzer._identify_project_type("build an API"), "backend_api")
        self.assertEqual(analyzer._identify_project_type("create a UI"), "frontend_application")
        self.assertEqual(analyzer._identify_project_type("full-stack app"), "fullstack_application")
        self.assertEqual(analyzer._identify_project_type("mobile app"), "mobile_application")
    
    def test_tech_description_mapping(self):
        """Test technology description mapping."""
        analyzer = RequestAnalyzer(self.mock_provider_manager)
        
        descriptions = [
            analyzer._get_tech_description("react"),
            analyzer._get_tech_description("fastapi"),
            analyzer._get_tech_description("postgresql")
        ]
        
        for desc in descriptions:
            self.assertIsInstance(desc, str)
            self.assertGreater(len(desc), 0)
    
    def test_confidence_calculation(self):
        """Test confidence calculation."""
        analyzer = RequestAnalyzer(self.mock_provider_manager)
        
        request = "Build a React application"
        confidence = analyzer._calculate_confidence(request, "react", "react")
        
        self.assertGreaterEqual(confidence, 0.0)
        self.assertLessEqual(confidence, 1.0)
    
    def test_alternative_technologies(self):
        """Test alternative technology suggestions."""
        analyzer = RequestAnalyzer(self.mock_provider_manager)
        
        alternatives = analyzer._get_alternatives("frontend", "react")
        self.assertIsInstance(alternatives, list)
        self.assertGreater(len(alternatives), 0)
    
    def test_default_questions_generation(self):
        """Test default follow-up questions generation."""
        analyzer = RequestAnalyzer(self.mock_provider_manager)
        
        components = []
        tech_stack = {}
        questions = analyzer._generate_default_questions(components, tech_stack)
        
        self.assertIsInstance(questions, list)
        self.assertGreater(len(questions), 0)
    
    def test_prompt_classification(self):
        """Test prompt classification."""
        optimizer = PromptOptimizer(self.mock_provider_manager, self.mock_learning_engine)
        
        # Test different request types
        self.assertEqual(optimizer._classify_request("create a function"), "code_generation")
        self.assertEqual(optimizer._classify_request("refactor this code"), "refactoring")
        self.assertEqual(optimizer._classify_request("fix this bug"), "debugging")
        self.assertEqual(optimizer._classify_request("write tests"), "testing")
        self.assertEqual(optimizer._classify_request("document this"), "documentation")
        self.assertEqual(optimizer._classify_request("design architecture"), "architecture")
    
    def test_complexity_estimation_prompt(self):
        """Test complexity estimation for prompts."""
        optimizer = PromptOptimizer(self.mock_provider_manager, self.mock_learning_engine)
        
        # Test different complexity levels
        self.assertEqual(optimizer._estimate_complexity("short"), "simple")
        self.assertEqual(optimizer._estimate_complexity("medium length request"), "medium")
        self.assertEqual(optimizer._estimate_complexity("very long request with many details"), "complex")
    
    def test_keyword_extraction(self):
        """Test technical keyword extraction."""
        optimizer = PromptOptimizer(self.mock_provider_manager, self.mock_learning_engine)
        
        request = "Build an API with authentication and database integration"
        keywords = optimizer._extract_keywords(request)
        
        self.assertIsInstance(keywords, list)
        self.assertGreater(len(keywords), 0)
    
    def test_confidence_scoring(self):
        """Test confidence scoring for prompt optimization."""
        optimizer = PromptOptimizer(self.mock_provider_manager, self.mock_learning_engine)
        
        original = "short"
        optimized = "Please create a short function with proper error handling and documentation"
        task_type = "code_generation"
        
        score = optimizer._calculate_confidence(original, optimized, task_type)
        
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 1.0)
    
    def test_learning_engine_statistics(self):
        """Test learning engine statistics."""
        engine = LearningEngine()
        
        # Test statistics methods
        self.assertEqual(engine.get_total_attempts(), 0)
        self.assertEqual(engine.get_success_rate(), 0.0)
        self.assertEqual(engine.get_average_confidence(), 0.0)
        self.assertEqual(engine.get_task_type_distribution(), {})
    
    def test_learning_engine_export_import(self):
        """Test learning engine data export and import."""
        engine = LearningEngine()
        
        # Test export
        export_data = engine.export_learning_data()
        self.assertIsInstance(export_data, dict)
        self.assertIn("export_timestamp", export_data)
        self.assertIn("prompt_attempts", export_data)
        self.assertIn("learning_patterns", export_data)
        self.assertIn("user_preferences", export_data)
        
        # Test import
        imported_count = engine.import_learning_data(export_data)
        self.assertIsInstance(imported_count, int)
    
    def test_user_preferences(self):
        """Test user preferences management."""
        engine = LearningEngine()
        
        # Test setting preferences
        engine.set_user_preference("coding_style", "python")
        engine.set_user_preference("framework", "fastapi")
        
        # Test getting preferences
        preferences = engine.get_user_preferences()
        self.assertIn("coding_style", preferences)
        self.assertIn("framework", preferences)
        self.assertEqual(preferences["coding_style"], "python")
        self.assertEqual(preferences["framework"], "fastapi")

class TestIntegration(unittest.TestCase):
    """Integration tests for the orchestrator."""
    
    @patch('orchestrator.config.config_manager')
    def test_full_workflow(self, mock_config):
        """Test the full orchestration workflow."""
        # This would test the complete workflow
        # For now, we'll just verify the test structure
        self.assertTrue(True)

def run_tests():
    """Run all tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases using default loader (makeSuite is deprecated/removed)
    loader = unittest.defaultTestLoader
    test_suite.addTests(loader.loadTestsFromTestCase(TestOrchestrator))
    test_suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print(f"\nFAILURES:")
        for test, traceback in result.failures:
            print(f"  {test}: {traceback}")
    
    if result.errors:
        print(f"\nERRORS:")
        for test, traceback in result.errors:
            print(f"  {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    print("🧪 Running AI Coding Orchestrator Tests")
    print("=" * 60)
    
    success = run_tests()
    
    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
