#!/usr/bin/env python3
"""
Installation script for the AI Coding Orchestrator.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_core_dependencies():
    """Install core dependencies."""
    return run_command(
        "pip install fastapi uvicorn python-dotenv typing-extensions pydantic click rich pyyaml requests aiofiles python-multipart",
        "Installing core dependencies"
    )

def install_ai_providers():
    """Install AI provider dependencies."""
    print("\n🤖 AI Provider Dependencies")
    print("These are optional - install only what you need:")
    
    providers = [
        ("openai", "OpenAI GPT integration"),
        ("google-generativeai", "Google Gemini integration"),
        ("anthropic", "Claude integration")
    ]
    
    for package, description in providers:
        install = input(f"Install {description} ({package})? [y/N]: ").lower().strip()
        if install == 'y':
            run_command(f"pip install {package}", f"Installing {description}")

def install_optional_features():
    """Install optional feature dependencies."""
    print("\n🔧 Optional Features")
    print("These enhance functionality but are not required:")
    
    features = [
        ("watchdog", "File monitoring"),
        ("pyperclip", "Clipboard integration")
    ]
    
    for package, description in features:
        install = input(f"Install {description} ({package})? [y/N]: ").lower().strip()
        if install == 'y':
            run_command(f"pip install {package}", f"Installing {description}")

def create_config_file():
    """Create a default configuration file."""
    config_content = """# AI Coding Orchestrator Configuration

# AI Provider API Keys (set these in environment variables)
# OPENAI_API_KEY=your_openai_key_here
# GOOGLE_AI_API_KEY=your_google_ai_key_here
# ANTHROPIC_API_KEY=your_anthropic_key_here

# Learning System Configuration
learning:
  enable_learning: true
  database_path: "data/patterns.db"
  pattern_threshold: 0.8
  max_patterns: 1000

# Cursor Integration Configuration
cursor:
  instruction_file_path: "cursor_instructions.md"
  clipboard_integration: true
  file_monitoring: true
  auto_validation: true

# File Management Configuration
file_manager:
  monitor_project_directory: true
  auto_validation: true
  validation_rules:
    max_line_length: 120
    require_docstrings: true
"""
    
    config_path = Path("config.yaml")
    if not config_path.exists():
        with open(config_path, 'w') as f:
            f.write(config_content)
        print("✅ Created default configuration file: config.yaml")
    else:
        print("ℹ️ Configuration file already exists: config.yaml")

def create_directories():
    """Create necessary directories."""
    directories = ["data", "exports", "logs"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}/")

def run_tests():
    """Run basic tests to verify installation."""
    print("\n🧪 Running basic tests...")
    
    try:
        # Test basic imports
        import orchestrator
        print("✅ Basic imports successful")
        
        # Test configuration
        from orchestrator.config import config_manager
        print("✅ Configuration system working")
        
        print("✅ Basic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Basic tests failed: {e}")
        return False

def main():
    """Main installation function."""
    print("🚀 AI Coding Orchestrator - Installation")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_core_dependencies():
        print("❌ Core dependencies installation failed")
        sys.exit(1)
    
    # Install AI providers (optional)
    install_ai_providers()
    
    # Install optional features
    install_optional_features()
    
    # Create configuration and directories
    create_config_file()
    create_directories()
    
    # Run tests
    if run_tests():
        print("\n🎉 Installation completed successfully!")
        print("\nNext steps:")
        print("1. Set your API keys in environment variables or config.yaml")
        print("2. Run: python main.py --config")
        print("3. Try: python main.py --health")
        print("4. Start using: python main.py 'Your coding request here'")
    else:
        print("\n⚠️ Installation completed with warnings")
        print("Some features may not work correctly")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Installation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed: {e}")
        sys.exit(1)
