"""
AI Coding Orchestrator - Configuration Module
Handles all system configuration, environment variables, and settings
"""

import os
from pathlib import Path
from typing import Optional, List
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class AIConfig:
    """Configuration for AI providers"""
    openai_api_key: Optional[str] = None
    google_ai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    preferred_provider: str = "auto"
    fallback_provider: str = "openai"
    max_tokens: int = 4000
    request_timeout: int = 60
    max_retries: int = 3

@dataclass
class SystemConfig:
    """System-wide configuration"""
    log_level: str = "INFO"
    enable_learning: bool = True
    learning_db_path: str = "data/patterns.db"
    template_dir: str = "data/templates"
    watch_patterns: List[str] = None
    ignore_patterns: List[str] = None

@dataclass
class CursorConfig:
    """Cursor integration configuration"""
    integration_mode: str = "file_based"  # file_based, clipboard, monitoring
    instruction_file_path: str = ".cursor_instructions.md"
    auto_generate_instructions: bool = True

class Config:
    """Main configuration class"""
    
    def __init__(self):
        self.ai = AIConfig(
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            google_ai_api_key=os.getenv("GOOGLE_AI_API_KEY"),
            anthropic_api_key=os.getenv("ANTHROPIC_API_KEY"),
            preferred_provider=os.getenv("PREFERRED_AI_PROVIDER", "auto"),
            fallback_provider=os.getenv("FALLBACK_AI_PROVIDER", "openai"),
            max_tokens=int(os.getenv("MAX_TOKENS", "4000")),
            request_timeout=int(os.getenv("REQUEST_TIMEOUT", "60")),
            max_retries=int(os.getenv("MAX_RETRIES", "3"))
        )
        
        self.system = SystemConfig(
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            enable_learning=os.getenv("ENABLE_LEARNING", "true").lower() == "true",
            learning_db_path=os.getenv("LEARNING_DB_PATH", "data/patterns.db"),
            template_dir=os.getenv("TEMPLATE_DIR", "data/templates"),
            watch_patterns=os.getenv("WATCH_PATTERNS", "*.py,*.js,*.ts,*.jsx,*.tsx,*.html,*.css").split(","),
            ignore_patterns=os.getenv("IGNORE_PATTERNS", "__pycache__/*,node_modules/*,.git/*").split(",")
        )
        
        self.cursor = CursorConfig(
            integration_mode=os.getenv("CURSOR_INTEGRATION_MODE", "file_based"),
            instruction_file_path=os.getenv("INSTRUCTION_FILE_PATH", ".cursor_instructions.md"),
            auto_generate_instructions=os.getenv("AUTO_GENERATE_INSTRUCTIONS", "true").lower() == "true"
        )
        
        # Ensure directories exist
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure required directories exist"""
        Path(self.system.learning_db_path).parent.mkdir(parents=True, exist_ok=True)
        Path(self.system.template_dir).mkdir(parents=True, exist_ok=True)
    
    def validate(self) -> bool:
        """Validate configuration"""
        # Check if at least one AI provider is configured
        if not any([self.ai.openai_api_key, self.ai.google_ai_api_key, self.ai.anthropic_api_key]):
            return False
        
        # Validate integration mode
        if self.cursor.integration_mode not in ["file_based", "clipboard", "monitoring"]:
            return False
        
        return True
    
    def get_available_providers(self) -> List[str]:
        """Get list of available AI providers"""
        providers = []
        if self.ai.openai_api_key:
            providers.append("openai")
        if self.ai.google_ai_api_key:
            providers.append("gemini")
        if self.ai.anthropic_api_key:
            providers.append("claude")
        return providers

# Global configuration instance
config = Config()
