"""
AI Provider Manager
Intelligently routes requests to the best available AI provider
"""

import asyncio
from typing import Dict, Any, List, Optional
from .base_provider import <PERSON><PERSON><PERSON>ider, AIRequest, AIResponse, TaskType
from .openai_client import OpenA<PERSON>rovider
from .gemini_client import <PERSON><PERSON><PERSON><PERSON>
from .claude_client import <PERSON><PERSON><PERSON><PERSON>
from config import config

class AIProviderManager:
    """Manages multiple AI providers and routes requests intelligently"""
    
    def __init__(self):
        self.providers: Dict[str, AIProvider] = {}
        self.provider_stats: Dict[str, Dict[str, Any]] = {}
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize all available AI providers"""
        # Initialize OpenAI
        if config.ai.openai_api_key:
            try:
                self.providers["openai"] = OpenAIProvider(
                    config.ai.openai_api_key,
                    {"max_tokens": config.ai.max_tokens}
                )
                self.provider_stats["openai"] = {
                    "requests": 0,
                    "successes": 0,
                    "failures": 0,
                    "total_cost": 0.0,
                    "avg_response_time": 0.0
                }
            except Exception as e:
                print(f"Failed to initialize OpenAI provider: {e}")
        
        # Initialize Gemini
        if config.ai.google_ai_api_key:
            try:
                self.providers["gemini"] = GeminiProvider(
                    config.ai.google_ai_api_key,
                    {"max_tokens": config.ai.max_tokens}
                )
                self.provider_stats["gemini"] = {
                    "requests": 0,
                    "successes": 0,
                    "failures": 0,
                    "total_cost": 0.0,
                    "avg_response_time": 0.0
                }
            except Exception as e:
                print(f"Failed to initialize Gemini provider: {e}")
        
        # Initialize Claude
        if config.ai.anthropic_api_key:
            try:
                self.providers["claude"] = ClaudeProvider(
                    config.ai.anthropic_api_key,
                    {"max_tokens": config.ai.max_tokens}
                )
                self.provider_stats["claude"] = {
                    "requests": 0,
                    "successes": 0,
                    "failures": 0,
                    "total_cost": 0.0,
                    "avg_response_time": 0.0
                }
            except Exception as e:
                print(f"Failed to initialize Claude provider: {e}")
    
    async def health_check_all(self):
        """Check health of all providers"""
        health_results = {}
        for name, provider in self.providers.items():
            try:
                is_healthy = await provider.health_check()
                health_results[name] = is_healthy
            except Exception as e:
                health_results[name] = False
                print(f"Health check failed for {name}: {e}")
        
        return health_results
    
    def get_available_providers(self) -> List[str]:
        """Get list of available provider names"""
        return list(self.providers.keys())
    
    def get_provider_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all providers"""
        info = {}
        for name, provider in self.providers.items():
            info[name] = {
                **provider.get_provider_info(),
                "stats": self.provider_stats.get(name, {})
            }
        return info
    
    def _select_best_provider(self, request: AIRequest) -> str:
        """Select the best provider for the given request"""
        available_providers = []
        
        # Filter providers by availability and suitability
        for name, provider in self.providers.items():
            if provider.is_available and provider.is_suitable_for_task(request.task_type):
                available_providers.append(name)
        
        if not available_providers:
            raise Exception("No suitable AI providers available")
        
        # If user has a preferred provider and it's available, use it
        if config.ai.preferred_provider != "auto" and config.ai.preferred_provider in available_providers:
            return config.ai.preferred_provider
        
        # Otherwise, select based on task type and provider strengths
        if request.task_type == TaskType.ANALYSIS:
            # Claude is excellent for analysis
            if "claude" in available_providers:
                return "claude"
            # Gemini is also good for analysis
            elif "gemini" in available_providers:
                return "gemini"
            # OpenAI as fallback
            else:
                return available_providers[0]
        
        elif request.task_type == TaskType.CODE_GENERATION:
            # OpenAI GPT-4 is excellent for code generation
            if "openai" in available_providers:
                return "openai"
            # Claude is also good
            elif "claude" in available_providers:
                return "claude"
            else:
                return available_providers[0]
        
        elif request.task_type == TaskType.OPTIMIZATION:
            # Claude is excellent for optimization
            if "claude" in available_providers:
                return "claude"
            # OpenAI is also good
            elif "openai" in available_providers:
                return "openai"
            else:
                return available_providers[0]
        
        else:
            # For other tasks, use the first available provider
            return available_providers[0]
    
    async def generate_response(self, request: AIRequest) -> AIResponse:
        """Generate response using the best available provider"""
        start_time = asyncio.get_event_loop().time()
        
        # Select the best provider
        provider_name = self._select_best_provider(request)
        provider = self.providers[provider_name]
        
        # Update stats
        self.provider_stats[provider_name]["requests"] += 1
        
        try:
            # Generate response
            response = await provider.generate_response(request)
            
            # Update success stats
            self.provider_stats[provider_name]["successes"] += 1
            self.provider_stats[provider_name]["total_cost"] += response.cost or 0.0
            
            # Calculate response time
            response_time = asyncio.get_event_loop().time() - start_time
            current_avg = self.provider_stats[provider_name]["avg_response_time"]
            total_requests = self.provider_stats[provider_name]["successes"]
            self.provider_stats[provider_name]["avg_response_time"] = (
                (current_avg * (total_requests - 1) + response_time) / total_requests
            )
            
            return response
            
        except Exception as e:
            # Update failure stats
            self.provider_stats[provider_name]["failures"] += 1
            
            # Try fallback provider if available
            if config.ai.fallback_provider in self.providers and config.ai.fallback_provider != provider_name:
                print(f"Primary provider {provider_name} failed, trying fallback {config.ai.fallback_provider}")
                fallback_provider = self.providers[config.ai.fallback_provider]
                
                try:
                    response = await fallback_provider.generate_response(request)
                    self.provider_stats[config.ai.fallback_provider]["requests"] += 1
                    self.provider_stats[config.ai.fallback_provider]["successes"] += 1
                    return response
                except Exception as fallback_error:
                    raise Exception(f"Both primary ({provider_name}) and fallback ({config.ai.fallback_provider}) providers failed: {str(e)}, {str(fallback_error)}")
            
            raise e
    
    def get_cost_summary(self) -> Dict[str, float]:
        """Get cost summary for all providers"""
        return {
            name: stats["total_cost"]
            for name, stats in self.provider_stats.items()
        }
    
    def get_success_rate(self) -> Dict[str, float]:
        """Get success rate for all providers"""
        rates = {}
        for name, stats in self.provider_stats.items():
            total = stats["requests"]
            if total > 0:
                rates[name] = stats["successes"] / total
            else:
                rates[name] = 0.0
        return rates
