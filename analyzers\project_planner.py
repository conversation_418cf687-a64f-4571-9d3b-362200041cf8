"""
Project Planner
Generates comprehensive project architectures, file structures, and development roadmaps
"""

import os
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from .request_analyzer import RequestAnalysis, TechnologyCategory
from ai_providers import AIProviderManager, AIRequest, TaskType

@dataclass
class FileStructure:
    """File structure specification"""
    path: str
    type: str  # file, directory
    content: Optional[str] = None
    description: str = ""
    is_generated: bool = False
    dependencies: List[str] = field(default_factory=list)

@dataclass
class ProjectArchitecture:
    """Complete project architecture specification"""
    project_name: str
    description: str
    file_structure: List[FileStructure]
    database_schema: Optional[Dict[str, Any]] = None
    api_endpoints: List[Dict[str, Any]] = field(default_factory=list)
    dependencies: Dict[str, str] = field(default_factory=dict)
    environment_variables: List[str] = field(default_factory=list)
    setup_instructions: List[str] = field(default_factory=list)
    development_phases: List[Dict[str, Any]] = field(default_factory=list)

class ProjectPlanner:
    """Plans and generates comprehensive project architectures"""
    
    def __init__(self, ai_manager: AIProviderManager):
        self.ai_manager = ai_manager
        
        # Common project templates
        self.project_templates = {
            "react_fastapi_postgres": {
                "name": "React + FastAPI + PostgreSQL",
                "description": "Full-stack web application with React frontend, FastAPI backend, and PostgreSQL database",
                "technologies": [TechnologyCategory.FRONTEND, TechnologyCategory.BACKEND, TechnologyCategory.DATABASE]
            },
            "react_node_mongodb": {
                "name": "React + Node.js + MongoDB",
                "description": "Full-stack web application with React frontend, Node.js backend, and MongoDB database",
                "technologies": [TechnologyCategory.FRONTEND, TechnologyCategory.BACKEND, TechnologyCategory.DATABASE]
            },
            "python_cli": {
                "name": "Python CLI Application",
                "description": "Command-line interface application built with Python",
                "technologies": [TechnologyCategory.BACKEND]
            },
            "python_web": {
                "name": "Python Web Application",
                "description": "Web application built with Python (Flask/Django)",
                "technologies": [TechnologyCategory.BACKEND]
            }
        }
    
    async def plan_project(self, analysis: RequestAnalysis) -> ProjectArchitecture:
        """Generate comprehensive project plan based on analysis"""
        # Select appropriate project template
        template = self._select_template(analysis)
        
        # Generate project architecture using AI
        ai_architecture = await self._generate_ai_architecture(analysis, template)
        
        # Create file structure
        file_structure = self._create_file_structure(analysis, ai_architecture)
        
        # Generate dependencies and setup
        dependencies = self._generate_dependencies(analysis)
        setup_instructions = self._generate_setup_instructions(analysis, file_structure)
        
        # Create development phases
        development_phases = self._create_development_phases(analysis)
        
        return ProjectArchitecture(
            project_name=self._generate_project_name(analysis),
            description=analysis.summary,
            file_structure=file_structure,
            database_schema=ai_architecture.get("database_schema"),
            api_endpoints=ai_architecture.get("api_endpoints", []),
            dependencies=dependencies,
            environment_variables=ai_architecture.get("environment_variables", []),
            setup_instructions=setup_instructions,
            development_phases=development_phases
        )
    
    def _select_template(self, analysis: RequestAnalysis) -> str:
        """Select appropriate project template based on analysis"""
        tech_categories = [tech.category for tech in analysis.technologies]
        
        if (TechnologyCategory.FRONTEND in tech_categories and 
            TechnologyCategory.BACKEND in tech_categories and 
            TechnologyCategory.DATABASE in tech_categories):
            # Full-stack application
            if any("postgres" in tech.name.lower() for tech in analysis.technologies):
                return "react_fastapi_postgres"
            else:
                return "react_node_mongodb"
        elif TechnologyCategory.BACKEND in tech_categories:
            if TechnologyCategory.FRONTEND in tech_categories:
                return "python_web"
            else:
                return "python_cli"
        else:
            return "react_fastapi_postgres"  # Default template
    
    async def _generate_ai_architecture(self, analysis: RequestAnalysis, template: str) -> Dict[str, Any]:
        """Use AI to generate detailed project architecture"""
        system_message = """You are an expert software architect. 
        Generate a detailed project architecture including:
        1. Database schema (if database is needed)
        2. API endpoints (if backend is needed)
        3. Environment variables needed
        4. Key implementation details
        
        Provide the response in JSON format."""
        
        ai_request = AIRequest(
            prompt=f"""Based on this project analysis, generate a detailed architecture:

Project: {analysis.summary}
Complexity: {analysis.complexity.value}
Technologies: {[f"{tech.name} ({tech.category.value})" for tech in analysis.technologies]}
Requirements: {[req.description for req in analysis.requirements]}

Template: {template}

Generate a JSON response with:
{{
    "database_schema": {{
        "tables": [
            {{
                "name": "table_name",
                "columns": [
                    {{"name": "column_name", "type": "data_type", "constraints": "constraints"}}
                ],
                "relationships": ["relationship descriptions"]
            }}
        ]
    }},
    "api_endpoints": [
        {{
            "method": "GET|POST|PUT|DELETE",
            "path": "/api/endpoint",
            "description": "What this endpoint does",
            "request_body": "Expected request structure",
            "response": "Expected response structure"
        }}
    ],
    "environment_variables": ["VAR_NAME=description"],
    "key_implementation_details": ["Detail 1", "Detail 2"]
}}""",
            task_type=TaskType.PLANNING,
            system_message=system_message,
            max_tokens=2000
        )
        
        try:
            response = await self.ai_manager.generate_response(ai_request)
            import json
            return json.loads(response.content)
        except Exception as e:
            print(f"AI architecture generation failed: {e}")
            return self._fallback_architecture(analysis, template)
    
    def _fallback_architecture(self, analysis: RequestAnalysis, template: str) -> Dict[str, Any]:
        """Fallback architecture when AI fails"""
        if template == "react_fastapi_postgres":
            return {
                "database_schema": {
                    "tables": [
                        {
                            "name": "users",
                            "columns": [
                                {"name": "id", "type": "INTEGER", "constraints": "PRIMARY KEY"},
                                {"name": "username", "type": "VARCHAR(50)", "constraints": "UNIQUE NOT NULL"},
                                {"name": "email", "type": "VARCHAR(100)", "constraints": "UNIQUE NOT NULL"},
                                {"name": "created_at", "type": "TIMESTAMP", "constraints": "DEFAULT NOW()"}
                            ],
                            "relationships": []
                        }
                    ]
                },
                "api_endpoints": [
                    {
                        "method": "GET",
                        "path": "/api/users",
                        "description": "Get all users",
                        "request_body": "None",
                        "response": "List of users"
                    },
                    {
                        "method": "POST",
                        "path": "/api/users",
                        "description": "Create new user",
                        "request_body": "User data",
                        "response": "Created user"
                    }
                ],
                "environment_variables": [
                    "DATABASE_URL=postgresql://user:password@localhost/dbname",
                    "SECRET_KEY=your-secret-key-here"
                ],
                "key_implementation_details": [
                    "Use SQLAlchemy for database ORM",
                    "Implement JWT authentication",
                    "Use Pydantic for data validation"
                ]
            }
        else:
            return {
                "database_schema": None,
                "api_endpoints": [],
                "environment_variables": [],
                "key_implementation_details": ["Implement core functionality"]
            }
    
    def _create_file_structure(self, analysis: RequestAnalysis, ai_architecture: Dict[str, Any]) -> List[FileStructure]:
        """Create comprehensive file structure for the project"""
        file_structure = []
        
        # Root level files
        file_structure.extend([
            FileStructure("README.md", "file", description="Project documentation"),
            FileStructure("requirements.txt", "file", description="Python dependencies"),
            FileStructure(".env.example", "file", description="Environment variables template"),
            FileStructure(".gitignore", "file", description="Git ignore file"),
            FileStructure("docker-compose.yml", "file", description="Docker configuration"),
        ])
        
        # Frontend structure
        if any(tech.category == TechnologyCategory.FRONTEND for tech in analysis.technologies):
            file_structure.extend([
                FileStructure("frontend/", "directory", description="Frontend application"),
                FileStructure("frontend/package.json", "file", description="Node.js dependencies"),
                FileStructure("frontend/src/", "directory", description="Source code"),
                FileStructure("frontend/src/components/", "directory", description="React components"),
                FileStructure("frontend/src/pages/", "directory", description="Page components"),
                FileStructure("frontend/src/utils/", "directory", description="Utility functions"),
                FileStructure("frontend/public/", "directory", description="Public assets"),
            ])
        
        # Backend structure
        if any(tech.category == TechnologyCategory.BACKEND for tech in analysis.technologies):
            file_structure.extend([
                FileStructure("backend/", "directory", description="Backend application"),
                FileStructure("backend/main.py", "file", description="Main application entry point"),
                FileStructure("backend/app/", "directory", description="Application package"),
                FileStructure("backend/app/api/", "directory", description="API routes"),
                FileStructure("backend/app/models/", "directory", description="Data models"),
                FileStructure("backend/app/services/", "directory", description="Business logic"),
                FileStructure("backend/app/utils/", "directory", description="Utility functions"),
                FileStructure("backend/tests/", "directory", description="Test files"),
            ])
        
        # Database structure
        if any(tech.category == TechnologyCategory.DATABASE for tech in analysis.technologies):
            file_structure.extend([
                FileStructure("database/", "directory", description="Database files"),
                FileStructure("database/schema.sql", "file", description="Database schema"),
                FileStructure("database/migrations/", "directory", description="Database migrations"),
                FileStructure("database/seed_data.sql", "file", description="Sample data"),
            ])
        
        # DevOps structure
        if any(tech.category == TechnologyCategory.DEVOPS for tech in analysis.technologies):
            file_structure.extend([
                FileStructure("deployment/", "directory", description="Deployment configuration"),
                FileStructure("deployment/Dockerfile", "file", description="Docker configuration"),
                FileStructure("deployment/nginx.conf", "file", description="Nginx configuration"),
                FileStructure("deployment/docker-compose.prod.yml", "file", description="Production Docker setup"),
            ])
        
        return file_structure
    
    def _generate_dependencies(self, analysis: RequestAnalysis) -> Dict[str, str]:
        """Generate project dependencies based on technologies"""
        dependencies = {}
        
        for tech in analysis.technologies:
            if tech.category == TechnologyCategory.BACKEND:
                if "fastapi" in tech.name.lower():
                    dependencies.update({
                        "fastapi": "0.104.1",
                        "uvicorn": "0.24.0",
                        "sqlalchemy": "2.0.23",
                        "pydantic": "2.5.0",
                        "python-multipart": "0.0.6",
                        "python-jose": "3.3.0",
                        "passlib": "1.7.4",
                        "bcrypt": "4.1.2"
                    })
                elif "django" in tech.name.lower():
                    dependencies.update({
                        "django": "4.2.7",
                        "djangorestframework": "3.14.0",
                        "psycopg2-binary": "2.9.9"
                    })
            
            elif tech.category == TechnologyCategory.FRONTEND:
                if "react" in tech.name.lower():
                    dependencies.update({
                        "react": "^18.2.0",
                        "react-dom": "^18.2.0",
                        "react-router-dom": "^6.20.1",
                        "axios": "^1.6.2"
                    })
            
            elif tech.category == TechnologyCategory.DATABASE:
                if "postgresql" in tech.name.lower():
                    dependencies["psycopg2-binary"] = "2.9.9"
                elif "mongodb" in tech.name.lower():
                    dependencies["pymongo"] = "4.6.0"
        
        return dependencies
    
    def _generate_setup_instructions(self, analysis: RequestAnalysis, file_structure: List[FileStructure]) -> List[str]:
        """Generate setup instructions for the project"""
        instructions = [
            "Clone the repository",
            "Install Python dependencies: pip install -r requirements.txt"
        ]
        
        if any(tech.category == TechnologyCategory.FRONTEND for tech in analysis.technologies):
            instructions.extend([
                "Navigate to frontend directory: cd frontend",
                "Install Node.js dependencies: npm install",
                "Start development server: npm start"
            ])
        
        if any(tech.category == TechnologyCategory.BACKEND for tech in analysis.technologies):
            instructions.extend([
                "Navigate to backend directory: cd backend",
                "Set up environment variables: cp .env.example .env",
                "Start backend server: uvicorn main:app --reload"
            ])
        
        if any(tech.category == TechnologyCategory.DATABASE for tech in analysis.technologies):
            instructions.extend([
                "Set up database connection in .env file",
                "Run database migrations: python -m alembic upgrade head"
            ])
        
        instructions.extend([
            "Open http://localhost:3000 in your browser",
            "API documentation available at http://localhost:8000/docs"
        ])
        
        return instructions
    
    def _create_development_phases(self, analysis: RequestAnalysis) -> List[Dict[str, Any]]:
        """Create development phases based on analysis"""
        phases = []
        
        # Phase 1: Setup and Foundation
        phases.append({
            "phase": 1,
            "name": "Project Setup",
            "description": "Initialize project structure and basic configuration",
            "tasks": [
                "Create project directory structure",
                "Set up version control",
                "Configure development environment",
                "Install dependencies"
            ],
            "estimated_time": "1-2 days",
            "dependencies": []
        })
        
        # Phase 2: Core Development
        if any(tech.category == TechnologyCategory.BACKEND for tech in analysis.technologies):
            phases.append({
                "phase": 2,
                "name": "Backend Development",
                "description": "Implement backend API and business logic",
                "tasks": [
                    "Set up database models",
                    "Implement API endpoints",
                    "Add authentication and authorization",
                    "Write unit tests"
                ],
                "estimated_time": "3-5 days",
                "dependencies": ["Project Setup"]
            })
        
        if any(tech.category == TechnologyCategory.FRONTEND for tech in analysis.technologies):
            phases.append({
                "phase": 3,
                "name": "Frontend Development",
                "description": "Build user interface and frontend logic",
                "tasks": [
                    "Create React components",
                    "Implement routing",
                    "Add state management",
                    "Connect to backend API"
                ],
                "estimated_time": "4-6 days",
                "dependencies": ["Backend Development"]
            })
        
        # Phase 4: Integration and Testing
        phases.append({
            "phase": len(phases) + 1,
            "name": "Integration and Testing",
            "description": "Integrate components and perform testing",
            "tasks": [
                "End-to-end testing",
                "Performance optimization",
                "Security review",
                "Documentation updates"
            ],
            "estimated_time": "2-3 days",
            "dependencies": ["Frontend Development"]
        })
        
        # Phase 5: Deployment
        phases.append({
            "phase": len(phases) + 1,
            "name": "Deployment",
            "description": "Deploy application to production",
            "tasks": [
                "Production environment setup",
                "Database migration",
                "Application deployment",
                "Monitoring and logging setup"
            ],
            "estimated_time": "1-2 days",
            "dependencies": ["Integration and Testing"]
        })
        
        return phases
    
    def _generate_project_name(self, analysis: RequestAnalysis) -> str:
        """Generate a project name based on analysis"""
        # Extract key words from the request
        words = analysis.original_request.lower().split()
        
        # Look for descriptive words
        descriptive_words = []
        for word in words:
            if word not in ["build", "create", "make", "develop", "app", "application", "with", "and", "the", "a", "an"]:
                descriptive_words.append(word)
        
        if descriptive_words:
            # Use first few descriptive words
            name = "_".join(descriptive_words[:3])
            return name.replace("-", "_").replace(" ", "_")
        else:
            return "ai_generated_project"
