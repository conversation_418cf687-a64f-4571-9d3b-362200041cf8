"""
AI Coding Orchestrator - A groundbreaking system for intelligent development workflow management.
"""

__version__ = "1.0.0"
__author__ = "AI Coding Orchestrator Team"
__description__ = "Intelligent AI-powered development workflow orchestration"

from .orchestrator_main import OrchestratorMain
from .analyzers.request_analyzer import RequestAnalyzer
from .analyzers.project_planner import ProjectPlanner
from .ai_providers.provider_manager import AIProviderManager
from .prompt_engine.optimizer import PromptOptimizer
from .file_manager.integrator import CursorIntegrator

__all__ = [
    "OrchestratorMain",
    "RequestAnalyzer", 
    "ProjectPlanner",
    "AIProviderManager",
    "PromptOptimizer",
    "CursorIntegrator"
]
