"""
Project Planner for generating comprehensive project architecture and development roadmaps.
"""

import os
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from pathlib import Path
from ..ai_providers.base_provider import TaskType, AIRequest
from ..ai_providers.provider_manager import AIProviderManager
from .request_analyzer import RequestAnalysis

@dataclass
class ProjectFile:
    """Represents a file in the project structure."""
    path: str
    content_type: str  # code, config, documentation, test, etc.
    description: str
    dependencies: List[str]
    estimated_lines: int
    priority: str  # high, medium, low

@dataclass
class ProjectStructure:
    """Complete project structure and architecture."""
    project_name: str
    root_directory: str
    files: List[ProjectFile]
    directories: List[str]
    dependencies: Dict[str, List[str]]
    build_steps: List[str]
    run_instructions: List[str]

@dataclass
class DevelopmentPhase:
    """Represents a development phase in the project."""
    name: str
    description: str
    tasks: List[str]
    estimated_time: str
    dependencies: List[str]
    deliverables: List[str]
    priority: str

@dataclass
class DevelopmentRoadmap:
    """Complete development roadmap for the project."""
    phases: List[DevelopmentPhase]
    total_estimated_time: str
    critical_path: List[str]
    milestones: List[str]
    risk_mitigation: List[str]

class ProjectPlanner:
    """Generates comprehensive project architecture and development roadmaps."""
    
    def __init__(self, ai_provider_manager: AIProviderManager):
        self.ai_provider_manager = ai_provider_manager
        
        # Common project templates
        self.project_templates = {
            "react_frontend": {
                "files": [
                    "package.json", "README.md", "src/App.js", "src/index.js",
                    "public/index.html", "src/components/", "src/styles/"
                ],
                "dependencies": ["react", "react-dom", "react-scripts"]
            },
            "fastapi_backend": {
                "files": [
                    "main.py", "requirements.txt", "README.md", "app/",
                    "app/models/", "app/routers/", "app/database.py"
                ],
                "dependencies": ["fastapi", "uvicorn", "sqlalchemy", "pydantic"]
            },
            "postgresql_database": {
                "files": [
                    "database.sql", "migrations/", "seed_data.sql", "README.md"
                ],
                "dependencies": ["psycopg2", "alembic"]
            }
        }
    
    async def plan_project(self, analysis: RequestAnalysis) -> Dict[str, Any]:
        """Generate comprehensive project plan."""
        print(f"📋 Planning project: {analysis.project_type}")
        
        # Generate project structure
        project_structure = await self._generate_project_structure(analysis)
        
        # Generate development roadmap
        development_roadmap = await self._generate_development_roadmap(analysis, project_structure)
        
        # Generate technology recommendations
        tech_recommendations = await self._generate_tech_recommendations(analysis)
        
        return {
            "project_structure": project_structure,
            "development_roadmap": development_roadmap,
            "technology_recommendations": tech_recommendations,
            "summary": self._generate_project_summary(analysis, project_structure, development_roadmap)
        }
    
    async def _generate_project_structure(self, analysis: RequestAnalysis) -> ProjectStructure:
        """Generate the complete project file structure."""
        project_name = self._generate_project_name(analysis)
        # Use the projects directory for better organization
        root_directory = f"projects/{project_name}"
        
        # Generate files based on technology stack
        files = []
        directories = []
        dependencies = {}
        
        # Frontend files
        if "frontend" in analysis.technology_stack:
            frontend_tech = analysis.technology_stack["frontend"]
            if frontend_tech == "react":
                files.extend(self._generate_react_files(project_name))
                directories.extend(["src", "src/components", "src/styles", "public"])
                dependencies["frontend"] = self.project_templates["react_frontend"]["dependencies"]
            elif frontend_tech == "vanilla":
                files.extend(self._generate_vanilla_files(project_name))
                directories.extend(["src", "src/styles"])
        # If no explicit frontend detected but project type is frontend, scaffold vanilla
        if "frontend" not in analysis.technology_stack and analysis.project_type == "frontend_application":
            files.extend(self._generate_vanilla_files(project_name))
            directories.extend(["src", "src/styles"])
        
        # Backend files
        if "backend" in analysis.technology_stack:
            backend_tech = analysis.technology_stack["backend"]
            if backend_tech == "fastapi":
                files.extend(self._generate_fastapi_files(project_name))
                directories.extend(["app", "app/models", "app/routers", "app/database"])
                dependencies["backend"] = self.project_templates["fastapi_backend"]["dependencies"]
        
        # Database files
        if "database" in analysis.technology_stack:
            db_tech = analysis.technology_stack["database"]
            if db_tech == "postgresql":
                files.extend(self._generate_database_files(project_name, db_tech))
                directories.extend(["migrations", "database"])
                dependencies["database"] = self.project_templates["postgresql_database"]["dependencies"]
        
        # Common project files
        files.extend(self._generate_common_files(project_name))
        
        # AI-enhanced file generation - only add actual source code files
        ai_files = await self._generate_ai_enhanced_files(analysis, files)
        # Filter out metadata files and only keep actual source code
        filtered_ai_files = [f for f in ai_files if self._is_valid_source_file(f.path)]
        files.extend(filtered_ai_files)
        
        project_structure = ProjectStructure(
            project_name=project_name,
            root_directory=root_directory,
            files=files,
            directories=list(set(directories)),  # Remove duplicates
            dependencies=dependencies,
            build_steps=self._generate_build_steps(analysis),
            run_instructions=self._generate_run_instructions(analysis)
        )
        
        # Create the actual project files and directories
        await self._create_project_files(project_structure)
        
        # Record successful project creation for learning
        await self._record_successful_project(analysis, project_structure)
        
        return project_structure
    
    async def _generate_development_roadmap(self, analysis: RequestAnalysis, structure: ProjectStructure) -> DevelopmentRoadmap:
        """Generate development roadmap with phases and tasks."""
        phases = []
        
        # Phase 1: Project Setup
        setup_phase = DevelopmentPhase(
            name="Project Setup",
            description="Initialize project structure and dependencies",
            tasks=[
                "Create project directory structure",
                "Initialize version control (git)",
                "Set up development environment",
                "Install dependencies",
                "Configure basic project settings"
            ],
            estimated_time="1-2 hours",
            dependencies=[],
            deliverables=["Project structure", "Basic configuration", "Development environment"],
            priority="high"
        )
        phases.append(setup_phase)
        
        # Phase 2: Core Development
        core_phase = DevelopmentPhase(
            name="Core Development",
            description="Develop main application features",
            tasks=self._generate_core_tasks(analysis, structure),
            estimated_time=self._estimate_core_development_time(analysis),
            dependencies=["Project Setup"],
            deliverables=["Working application", "Core functionality"],
            priority="high"
        )
        phases.append(core_phase)
        
        # Phase 3: Testing & Refinement
        testing_phase = DevelopmentPhase(
            name="Testing & Refinement",
            description="Test application and refine functionality",
            tasks=[
                "Write unit tests",
                "Integration testing",
                "User acceptance testing",
                "Bug fixes and refinements",
                "Performance optimization"
            ],
            estimated_time="2-4 hours",
            dependencies=["Core Development"],
            deliverables=["Tested application", "Bug fixes", "Performance improvements"],
            priority="medium"
        )
        phases.append(testing_phase)
        
        # Phase 4: Deployment
        deployment_phase = DevelopmentPhase(
            name="Deployment",
            description="Deploy application to production environment",
            tasks=self._generate_deployment_tasks(analysis),
            estimated_time="1-2 hours",
            dependencies=["Testing & Refinement"],
            deliverables=["Deployed application", "Production environment"],
            priority="medium"
        )
        phases.append(deployment_phase)
        
        # Calculate total time
        total_time = self._calculate_total_time(phases)
        
        # Generate critical path
        critical_path = self._generate_critical_path(phases)
        
        # Generate milestones
        milestones = self._generate_milestones(phases)
        
        # Generate risk mitigation
        risk_mitigation = self._generate_risk_mitigation(analysis)
        
        return DevelopmentRoadmap(
            phases=phases,
            total_estimated_time=total_time,
            critical_path=critical_path,
            milestones=milestones,
            risk_mitigation=risk_mitigation
        )
    
    async def _generate_tech_recommendations(self, analysis: RequestAnalysis) -> Dict[str, Any]:
        """Generate technology recommendations using AI."""
        try:
            prompt = f"""
            Based on this project analysis, provide technology recommendations:
            
            PROJECT: {analysis.original_request}
            COMPLEXITY: {analysis.complexity_level}
            TECH STACK: {analysis.technology_stack}
            
            Please recommend:
            1. Additional libraries/frameworks that would be beneficial
            2. Development tools and utilities
            3. Testing frameworks
            4. Deployment strategies
            5. Monitoring and logging solutions
            6. Security considerations
            
            Format as structured recommendations.
            """
            
            ai_request = AIRequest(
                prompt=prompt,
                task_type=TaskType.PLANNING,
                context={
                    "system_prompt": "You are an expert software architect providing technology recommendations."
                }
            )
            
            response = await self.ai_provider_manager.generate_response(ai_request)
            return self._parse_tech_recommendations(response.content)
            
        except Exception as e:
            print(f"AI tech recommendations failed: {e}")
            return self._generate_default_tech_recommendations(analysis)
    
    def _generate_project_name(self, analysis: RequestAnalysis) -> str:
        """Generate a clean, professional project name based on the analysis."""
        import re
        
        # Extract key words from the original request
        request = analysis.original_request.lower()
        
        # Define technology keywords and their clean names
        tech_mapping = {
            'react': 'React',
            'vue': 'Vue',
            'angular': 'Angular',
            'fastapi': 'FastAPI',
            'django': 'Django',
            'flask': 'Flask',
            'postgresql': 'PostgreSQL',
            'mysql': 'MySQL',
            'mongodb': 'MongoDB',
            'redis': 'Redis',
            'node': 'Node',
            'express': 'Express',
            'python': 'Python',
            'javascript': 'JavaScript',
            'typescript': 'TypeScript',
            'todo': 'Todo',
            'calculator': 'Calculator',
            'scraper': 'Scraper',
            'api': 'API',
            'web': 'Web',
            'mobile': 'Mobile',
            'desktop': 'Desktop'
        }
        
        # Extract relevant technologies
        detected_techs = []
        for tech, clean_name in tech_mapping.items():
            if tech in request:
                detected_techs.append(clean_name)
        
        # Generate project name
        if detected_techs:
            if len(detected_techs) == 1:
                project_name = f"{detected_techs[0]}App"
            elif len(detected_techs) == 2:
                project_name = f"{detected_techs[0]}{detected_techs[1]}App"
            else:
                project_name = f"{detected_techs[0]}{detected_techs[1]}App"
        else:
            # Fallback based on project type
            type_mapping = {
                "frontend_application": "FrontendApp",
                "backend_api": "BackendAPI",
                "full_stack": "FullStackApp",
                "web_application": "WebApp",
                "mobile_application": "MobileApp",
                "desktop_application": "DesktopApp"
            }
            project_name = type_mapping.get(analysis.project_type, "Project")
        
        # Clean the name: remove special characters, ensure it's valid
        clean_name = re.sub(r'[^a-zA-Z0-9]', '', project_name)
        
        # Ensure it starts with a letter
        if clean_name and not clean_name[0].isalpha():
            clean_name = "App" + clean_name
        
        # Ensure it's not empty
        if not clean_name:
            clean_name = "Project"
        
        return clean_name
    
    def _is_valid_source_file(self, file_path: str) -> bool:
        """Check if a file path represents a valid source code file."""
        # Valid source file extensions
        valid_extensions = {
            '.py', '.js', '.jsx', '.ts', '.tsx', '.html', '.css', '.scss', '.sass',
            '.json', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.env', '.md',
            '.sql', '.sh', '.bat', '.ps1', '.dockerfile', '.gitignore'
        }
        
        # Valid file names (not metadata)
        valid_names = {
            'readme.md', 'readme', 'license', '.gitignore', 'dockerfile',
            'requirements.txt', 'package.json', 'tsconfig.json', 'webpack.config.js',
            'main.py', 'app.py', 'index.js', 'app.js', 'index.html', 'main.css'
        }
        
        file_path_lower = file_path.lower()
        file_name = Path(file_path).name.lower()
        
        # Check if it contains common metadata indicators
        metadata_indicators = [
            'priority', 'estimated', 'purpose', 'description', 'file path',
            'high priority', 'medium priority', 'low priority', 'estimated loc',
            'explanation', 'choices', 'omissions', 'complex', 'build system',
            'testing', 'documentation', 'framework', 'unless', 'chosen'
        ]
        
        for indicator in metadata_indicators:
            if indicator in file_path_lower:
                return False
        
        # Check if it's a valid extension
        if Path(file_path).suffix.lower() in valid_extensions:
            return True
        
        # Check if it's a valid file name with no extension
        if file_name in valid_names:
            return True
        
        # Otherwise, not valid
        return False
    
    async def _record_successful_project(self, analysis: RequestAnalysis, project_structure: ProjectStructure) -> None:
        """Record successful project creation for learning and improvement."""
        try:
            # Create learning data
            learning_data = {
                "project_type": analysis.project_type,
                "complexity_level": analysis.complexity_level,
                "technology_stack": analysis.technology_stack,
                "file_count": len(project_structure.files),
                "directory_count": len(project_structure.directories),
                "successful_patterns": {
                    "file_structure": [f.path for f in project_structure.files],
                    "directory_structure": project_structure.directories,
                    "dependencies": project_structure.dependencies
                },
                "timestamp": time.time(),
                "request": analysis.original_request
            }
            
            # Store in learning system if available
            if hasattr(self, 'learning_engine'):
                try:
                    await self.learning_engine.record_successful_pattern(
                        "project_creation",
                        learning_data,
                        success_rate=1.0
                    )
                    print("📚 Project pattern recorded for learning")
                except Exception as e:
                    print(f"⚠️ Learning recording failed: {e}")
            
        except Exception as e:
            print(f"⚠️ Failed to record project for learning: {e}")
    
    def _generate_react_files(self, project_name: str) -> List[ProjectFile]:
        """Generate React frontend files."""
        return [
            ProjectFile(
                path="package.json",
                content_type="config",
                description="Node.js package configuration",
                dependencies=[],
                estimated_lines=30,
                priority="high"
            ),
            ProjectFile(
                path="src/App.js",
                content_type="code",
                description="Main React application component",
                dependencies=["react"],
                estimated_lines=50,
                priority="high"
            ),
            ProjectFile(
                path="src/index.js",
                content_type="code",
                description="React application entry point",
                dependencies=["react", "react-dom"],
                estimated_lines=20,
                priority="high"
            ),
            ProjectFile(
                path="public/index.html",
                content_type="markup",
                description="HTML template for React app",
                dependencies=[],
                estimated_lines=25,
                priority="high"
            )
        ]
    
    def _generate_fastapi_files(self, project_name: str) -> List[ProjectFile]:
        """Generate FastAPI backend files."""
        return [
            ProjectFile(
                path="main.py",
                content_type="code",
                description="FastAPI application entry point",
                dependencies=["fastapi", "uvicorn"],
                estimated_lines=40,
                priority="high"
            ),
            ProjectFile(
                path="requirements.txt",
                content_type="config",
                description="Python dependencies",
                dependencies=[],
                estimated_lines=10,
                priority="high"
            ),
            ProjectFile(
                path="app/__init__.py",
                content_type="code",
                description="App package initialization",
                dependencies=[],
                estimated_lines=5,
                priority="medium"
            ),
            ProjectFile(
                path="app/database.py",
                content_type="code",
                description="Database connection and models",
                dependencies=["sqlalchemy", "pydantic"],
                estimated_lines=60,
                priority="high"
            )
        ]
    
    def _generate_database_files(self, project_name: str, db_tech: str) -> List[ProjectFile]:
        """Generate database-related files."""
        return [
            ProjectFile(
                path="database/schema.sql",
                content_type="code",
                description=f"{db_tech} database schema",
                dependencies=[db_tech],
                estimated_lines=80,
                priority="high"
            ),
            ProjectFile(
                path="database/migrations/001_initial.sql",
                content_type="code",
                description="Initial database migration",
                dependencies=[db_tech],
                estimated_lines=40,
                priority="medium"
            )
        ]
    
    def _generate_common_files(self, project_name: str) -> List[ProjectFile]:
        """Generate common project files."""
        return [
            ProjectFile(
                path="README.md",
                content_type="documentation",
                description="Project documentation and setup instructions",
                dependencies=[],
                estimated_lines=100,
                priority="high"
            ),
            ProjectFile(
                path=".gitignore",
                content_type="config",
                description="Git ignore patterns",
                dependencies=[],
                estimated_lines=20,
                priority="medium"
            ),
            ProjectFile(
                path="LICENSE",
                content_type="documentation",
                description="Project license",
                dependencies=[],
                estimated_lines=20,
                priority="low"
            )
        ]
    
    async def _generate_ai_enhanced_files(self, analysis: RequestAnalysis, base_files: List[ProjectFile]) -> List[ProjectFile]:
        """Use AI to generate additional project-specific files."""
        try:
            prompt = f"""
            Based on this project, suggest additional files that would be beneficial:
            
            PROJECT: {analysis.original_request}
            COMPLEXITY: {analysis.complexity_level}
            EXISTING FILES: {[f.path for f in base_files]}
            
            Suggest additional files with:
            - File path
            - Purpose/description
            - Estimated complexity (lines of code)
            - Priority level
            
            Focus on practical, necessary files for a {analysis.complexity_level} complexity project.
            """
            
            ai_request = AIRequest(
                prompt=prompt,
                task_type=TaskType.PLANNING,
                context={
                    "system_prompt": "You are an expert software architect suggesting project files."
                }
            )
            
            response = await self.ai_provider_manager.generate_response(ai_request)
            return self._parse_ai_file_suggestions(response.content)
            
        except Exception as e:
            print(f"AI file generation failed: {e}")
            return []
    
    def _generate_core_tasks(self, analysis: RequestAnalysis, structure: ProjectStructure) -> List[str]:
        """Generate core development tasks."""
        tasks = []
        
        if "frontend" in analysis.technology_stack:
            tasks.extend([
                "Set up frontend development environment",
                "Create main application layout",
                "Implement core UI components",
                "Set up state management",
                "Implement routing and navigation"
            ])
        
        if "backend" in analysis.technology_stack:
            tasks.extend([
                "Set up backend development environment",
                "Create API endpoints",
                "Implement data models",
                "Set up database connections",
                "Implement business logic"
            ])
        
        if "database" in analysis.technology_stack:
            tasks.extend([
                "Design database schema",
                "Create database tables",
                "Set up data relationships",
                "Implement data validation",
                "Create seed data"
            ])
        
        return tasks
    
    def _estimate_core_development_time(self, analysis: RequestAnalysis) -> str:
        """Estimate time for core development."""
        complexity = analysis.complexity_level
        component_count = len(analysis.technology_stack)
        
        if complexity == "simple":
            base_time = 2
        elif complexity == "medium":
            base_time = 4
        elif complexity == "complex":
            base_time = 8
        else:  # enterprise
            base_time = 16
        
        # Adjust for number of components
        adjusted_time = base_time * (1 + (component_count - 1) * 0.3)
        
        if adjusted_time < 4:
            return f"{int(adjusted_time)}-{int(adjusted_time + 2)} hours"
        elif adjusted_time < 24:
            return f"{int(adjusted_time/8)}-{int(adjusted_time/8 + 1)} days"
        else:
            return f"{int(adjusted_time/8)}-{int(adjusted_time/8 + 2)} days"
    
    def _generate_deployment_tasks(self, analysis: RequestAnalysis) -> List[str]:
        """Generate deployment-related tasks."""
        tasks = [
            "Prepare production build",
            "Set up production environment",
            "Configure environment variables",
            "Deploy application",
            "Verify deployment",
            "Set up monitoring"
        ]
        
        # Add cloud-specific tasks
        if any(cloud in str(analysis.technology_stack).lower() for cloud in ["aws", "azure", "gcp"]):
            tasks.extend([
                "Configure cloud services",
                "Set up CI/CD pipeline",
                "Configure auto-scaling"
            ])
        
        return tasks
    
    def _calculate_total_time(self, phases: List[DevelopmentPhase]) -> str:
        """Calculate total estimated development time."""
        total_hours = 0
        
        for phase in phases:
            time_str = phase.estimated_time
            try:
                # Handle ranges like "1-2 days" or "3-4 hours"
                if "-" in time_str:
                    # Take the average of the range
                    parts = time_str.split("-")
                    if len(parts) == 2:
                        first_part = parts[0].strip()
                        second_part = parts[1].strip()
                        
                        # Extract numbers from strings like "1-2 days" or "3-4 hours"
                        first_num = float(''.join(filter(str.isdigit, first_part)))
                        second_num = float(''.join(filter(str.isdigit, second_part)))
                        avg_num = (first_num + second_num) / 2
                        
                        if "hours" in time_str.lower():
                            total_hours += avg_num
                        elif "days" in time_str.lower():
                            total_hours += avg_num * 8
                        elif "weeks" in time_str.lower():
                            total_hours += avg_num * 40
                        elif "months" in time_str.lower():
                            total_hours += avg_num * 160
                    else:
                        # Fallback: try to extract single number
                        num = float(''.join(filter(str.isdigit, time_str)))
                        if "hours" in time_str.lower():
                            total_hours += num
                        elif "days" in time_str.lower():
                            total_hours += num * 8
                        elif "weeks" in time_str.lower():
                            total_hours += num * 40
                        elif "months" in time_str.lower():
                            total_hours += num * 160
                else:
                    # Handle single values like "2 days" or "5 hours"
                    num = float(''.join(filter(str.isdigit, time_str)))
                    if "hours" in time_str.lower():
                        total_hours += num
                    elif "days" in time_str.lower():
                        total_hours += num * 8
                    elif "weeks" in time_str.lower():
                        total_hours += num * 40
                    elif "months" in time_str.lower():
                        total_hours += num * 160
            except (ValueError, AttributeError):
                # If parsing fails, use default estimates
                if "hours" in time_str.lower():
                    total_hours += 4  # Default 4 hours
                elif "days" in time_str.lower():
                    total_hours += 8  # Default 1 day
                elif "weeks" in time_str.lower():
                    total_hours += 40  # Default 1 week
                elif "months" in time_str.lower():
                    total_hours += 160  # Default 1 month
                else:
                    total_hours += 8  # Default 1 day
        
        if total_hours < 24:
            return f"{int(total_hours)} hours"
        elif total_hours < 160:
            days = total_hours / 8
            return f"{days:.1f} days"
        elif total_hours < 800:
            weeks = total_hours / 40
            return f"{weeks:.1f} weeks"
        else:
            months = total_hours / 160
            return f"{months:.1f} months"
    
    def _generate_critical_path(self, phases: List[DevelopmentPhase]) -> List[str]:
        """Generate critical path for the project."""
        critical_path = []
        
        for phase in phases:
            if phase.priority == "high":
                critical_path.append(phase.name)
        
        return critical_path
    
    def _generate_milestones(self, phases: List[DevelopmentPhase]) -> List[str]:
        """Generate project milestones."""
        milestones = []
        
        for i, phase in enumerate(phases):
            milestone = f"Phase {i+1}: {phase.name} - {phase.estimated_time}"
            milestones.append(milestone)
        
        return milestones
    
    def _generate_risk_mitigation(self, analysis: RequestAnalysis) -> List[str]:
        """Generate risk mitigation strategies."""
        risks = analysis.risk_factors
        mitigation = []
        
        for risk in risks:
            if "database" in risk.lower():
                mitigation.append("Implement database backup and recovery procedures")
            elif "security" in risk.lower():
                mitigation.append("Implement proper authentication and authorization")
            elif "performance" in risk.lower():
                mitigation.append("Add performance monitoring and optimization")
            else:
                mitigation.append(f"Address {risk} with proper planning and testing")
        
        return mitigation
    
    def _generate_build_steps(self, analysis: RequestAnalysis) -> List[str]:
        """Generate build steps for the project."""
        steps = []
        
        if "frontend" in analysis.technology_stack:
            steps.extend([
                "npm install",
                "npm run build"
            ])
        
        if "backend" in analysis.technology_stack:
            steps.extend([
                "pip install -r requirements.txt",
                "python -m uvicorn main:app --reload"
            ])
        
        return steps
    
    def _generate_run_instructions(self, analysis: RequestAnalysis) -> List[str]:
        """Generate run instructions for the project."""
        instructions = []
        
        if "frontend" in analysis.technology_stack:
            instructions.append("npm start")
        
        if "backend" in analysis.technology_stack:
            instructions.append("python main.py")
        
        return instructions
    
    def _parse_tech_recommendations(self, ai_response: str) -> Dict[str, Any]:
        """Parse AI-generated technology recommendations."""
        # Simplified parsing - in production, use more sophisticated parsing
        recommendations = {
            "libraries": [],
            "tools": [],
            "testing": [],
            "deployment": [],
            "monitoring": [],
            "security": []
        }
        
        # Basic parsing logic
        lines = ai_response.split('\n')
        current_category = None
        
        for line in lines:
            line_lower = line.lower()
            if "library" in line_lower or "framework" in line_lower:
                current_category = "libraries"
            elif "tool" in line_lower:
                current_category = "tools"
            elif "test" in line_lower:
                current_category = "testing"
            elif "deploy" in line_lower:
                current_category = "deployment"
            elif "monitor" in line_lower:
                current_category = "monitoring"
            elif "security" in line_lower:
                current_category = "security"
            elif current_category and line.strip().startswith(('-', '*', '•')):
                item = line.strip().lstrip('-*• ').strip()
                if item:
                    recommendations[current_category].append(item)
        
        return recommendations
    
    def _generate_default_tech_recommendations(self, analysis: RequestAnalysis) -> Dict[str, Any]:
        """Generate default technology recommendations."""
        return {
            "libraries": ["lodash", "moment", "axios"],
            "tools": ["eslint", "prettier", "husky"],
            "testing": ["jest", "react-testing-library"],
            "deployment": ["docker", "nginx"],
            "monitoring": ["sentry", "logrocket"],
            "security": ["helmet", "cors", "rate-limiting"]
        }
    
    def _parse_ai_file_suggestions(self, ai_response: str) -> List[ProjectFile]:
        """Parse AI-generated file suggestions."""
        files = []
        
        # Basic parsing - extract file paths and descriptions
        lines = ai_response.split('\n')
        for line in lines:
            if line.strip().startswith(('-', '*', '•')):
                parts = line.strip().lstrip('-*• ').split(':')
                if len(parts) >= 2:
                    raw_path = parts[0].strip()
                    path = self._sanitize_file_path(raw_path)
                    # Skip if sanitization failed or path still looks unsafe/formatting
                    if not path or any(ch in path for ch in ['`', '*', '"']):
                        continue
                    description = parts[1].strip()
                    
                    file = ProjectFile(
                        path=path,
                        content_type="code",
                        description=description,
                        dependencies=[],
                        estimated_lines=50,
                        priority="medium"
                    )
                    # Only keep valid source paths
                    if self._is_valid_source_file(file.path):
                        files.append(file)
        
        return files

    def _sanitize_file_path(self, raw_path: str) -> str:
        """Sanitize AI-suggested file paths by removing markdown/backticks and invalid characters.

        - Strips surrounding quotes/backticks/asterisks
        - Normalizes separators to '/'
        - Removes any path traversal or drive letters
        - Allows only [A-Za-z0-9._-/]
        """
        import re
        if not raw_path:
            return ""
        # Remove surrounding formatting characters and emphasis markers
        path = raw_path.strip().strip('`').strip('*').strip().strip('"').strip("'")
        # Normalize separators
        path = path.replace('\\', '/')
        # Remove leading ./ or /
        while path.startswith('./') or path.startswith('/'):
            path = path[2:] if path.startswith('./') else path[1:]
        # Disallow parent dirs
        path = path.replace('..', '')
        # Keep only allowed characters
        path = re.sub(r'[^A-Za-z0-9._\-/]', '', path)
        # Collapse multiple slashes
        path = re.sub(r'/+', '/', path)
        # Trim again
        path = path.strip()
        return path
    
    def _generate_project_summary(self, analysis: RequestAnalysis, structure: ProjectStructure, roadmap: DevelopmentRoadmap) -> str:
        """Generate a project summary."""
        summary = f"""
        🚀 PROJECT SUMMARY
        
        Project: {structure.project_name}
        Type: {analysis.project_type}
        Complexity: {analysis.complexity_level}
        Estimated Time: {getattr(roadmap, 'total_estimated_time', 'TBD')}
        
        Technology Stack:
        {chr(10).join([f"  - {category}: {tech}" for category, tech in analysis.technology_stack.items()])}
        
        Development Phases:
        {chr(10).join([f"  - {phase.name}: {phase.estimated_time}" for phase in roadmap.phases]) if hasattr(roadmap, 'phases') and roadmap.phases else "  - Project setup and planning"}
        
        Total Files: {len(structure.files)}
        Key Deliverables: {"Project structure and initial setup" if not hasattr(roadmap, 'phases') or not roadmap.phases else ', '.join([phase.deliverables[0] for phase in roadmap.phases if phase.deliverables])}
        """
        
        return summary.strip()
    
    async def _create_project_files(self, project_structure: ProjectStructure) -> None:
        """Create the actual project files and directories."""
        try:
            from pathlib import Path
            from textwrap import indent
            
            # Create project root directory
            project_path = Path(project_structure.root_directory)
            project_path.mkdir(parents=True, exist_ok=True)
            
            print(f"📁 Creating project: {project_path}")
            
            # Create subdirectories
            for directory in project_structure.directories:
                dir_path = project_path / directory
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"  📂 Created directory: {directory}")
            
            # Create project files
            for file_info in project_structure.files:
                file_path = project_path / file_info.path
                
                # Ensure parent directory exists
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Generate file content based on type
                content = self._generate_file_content(file_info, project_structure)
                
                # Write file
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"  📄 Created file: {file_info.path}")
                # Optional: show a brief preview of generated code/content
                try:
                    from ..config import config_manager
                    cfg = config_manager.get_config()
                    if getattr(cfg, 'stream_code_previews', False):
                        preview_lines = "\n".join(content.splitlines()[:20])
                        print(indent(preview_lines, prefix="      | "))
                        if len(content.splitlines()) > 20:
                            print("      | …")
                except Exception:
                    pass
            
            # Create README.md
            readme_path = project_path / "README.md"
            readme_content = self._generate_readme_content(project_structure)
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            print(f"  📖 Created README.md")
            
            # Create .gitignore if it doesn't exist
            gitignore_path = project_path / ".gitignore"
            if not gitignore_path.exists():
                gitignore_content = self._generate_gitignore_content(project_structure)
                with open(gitignore_path, 'w', encoding='utf-8') as f:
                    f.write(gitignore_content)
                print(f"  🚫 Created .gitignore")
            
            print(f"✅ Project created successfully at: {project_path}")
            
        except Exception as e:
            print(f"❌ Error creating project files: {e}")
    
    def _generate_file_content(self, file_info: ProjectFile, project_structure: ProjectStructure) -> str:
        """Generate high-quality, complete content for a project file."""
        file_path = file_info.path.lower()
        
        if "package.json" in file_path:
            return self._generate_package_json(project_structure)
        elif "requirements.txt" in file_path:
            return self._generate_requirements_txt(project_structure)
        elif "main.py" in file_path or "app.py" in file_path:
            return self._generate_main_py(project_structure)
        elif file_info.path.lower() == "index.html":
            return self._generate_vanilla_index_html(project_structure)
        elif "public/index.html" in file_info.path.lower():
            return self._generate_index_html(project_structure)
        elif file_info.path.lower() == "src/index.js":
            return self._generate_vanilla_index_js(project_structure)
        elif "app.js" in file_path:
            return self._generate_app_js(project_structure)
        elif "schema.sql" in file_path:
            return self._generate_schema_sql(project_structure)
        elif "database.py" in file_path:
            return self._generate_database_py(project_structure)
        elif "models.py" in file_path:
            return self._generate_models_py(project_structure)
        elif "routers.py" in file_path:
            return self._generate_routers_py(project_structure)
        elif "config.py" in file_path:
            return self._generate_config_py(project_structure)
        elif "utils.py" in file_path:
            return self._generate_utils_py(project_structure)
        elif "test_" in file_path:
            return self._generate_test_file(file_info, project_structure)
        elif file_info.path.lower() == 'src/styles/main.css':
            return self._generate_vanilla_main_css(project_structure)
        elif file_path.endswith('.css'):
            return self._generate_css_file(file_info, project_structure)
        elif file_path.endswith('.md'):
            return self._generate_markdown_file(file_info, project_structure)
        elif "/src/components/" in file_info.path.replace('\\', '/').lower() and file_info.path.lower().endswith('.js'):
            return self._generate_vanilla_component_js(file_info, project_structure)
        else:
            # Generate intelligent default content based on file type
            return self._generate_intelligent_default(file_info, project_structure)

    def _generate_vanilla_files(self, project_name: str) -> List[ProjectFile]:
        """Generate vanilla HTML/CSS/JS scaffolding for simple frontend sites."""
        return [
            ProjectFile(
                path="index.html",
                content_type="markup",
                description=f"Main HTML page for {project_name}",
                dependencies=[],
                estimated_lines=120,
                priority="high",
            ),
            ProjectFile(
                path="src/index.js",
                content_type="code",
                description="Main JavaScript logic with basic interactivity",
                dependencies=[],
                estimated_lines=160,
                priority="high",
            ),
            ProjectFile(
                path="src/styles/main.css",
                content_type="style",
                description="Responsive styling for the site",
                dependencies=[],
                estimated_lines=200,
                priority="high",
            ),
        ]

    def _generate_vanilla_index_html(self, project_structure: ProjectStructure) -> str:
        """Generate a clean vanilla HTML skeleton with tabs and content sections."""
        return f"""<!DOCTYPE html>
<html lang=\"en\">
<head>
  <meta charset=\"UTF-8\" />
  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />
  <title>{project_structure.project_name}</title>
  <link rel=\"stylesheet\" href=\"src/styles/main.css\" />
</head>
<body>
  <header class=\"site-header\">
    <div class=\"container\">
      <h1>{project_structure.project_name}</h1>
      <nav>
        <button id=\"tab-breeds\" class=\"active\">Breeds</button>
        <button id=\"tab-care\">Care</button>
        <button id=\"tab-facts\">Facts</button>
      </nav>
    </div>
  </header>

  <main class=\"container\">
    <section id=\"view-breeds\" class=\"view active\">
      <h2>Chicken Breeds</h2>
      <div id=\"breeds-list\" class=\"card-grid\"></div>
    </section>
    <section id=\"view-care\" class=\"view\">
      <h2>Care</h2>
      <ul id=\"care-list\"></ul>
    </section>
    <section id=\"view-facts\" class=\"view\">
      <h2>Interesting Facts</h2>
      <ul id=\"facts-list\"></ul>
    </section>
  </main>

  <footer class=\"site-footer\">
    <div class=\"container\">Built with vanilla JS • Generated by AI Coding Orchestrator</div>
  </footer>

  <script src=\"src/index.js\"></script>
  </body>
</html>
"""

    def _generate_vanilla_main_css(self, project_structure: ProjectStructure) -> str:
        """Generate responsive CSS for the vanilla frontend."""
        return """/* Main styles */
:root { --bg: #f8fafc; --fg: #111827; --muted: #6b7280; --brand: #2563eb; }
* { box-sizing: border-box; }
html, body { margin: 0; padding: 0; font-family: system-ui, -apple-system, Segoe UI, Roboto, sans-serif; color: var(--fg); background: var(--bg); }
.container { max-width: 1024px; margin: 0 auto; padding: 0 1rem; }
.site-header { background: #fff; border-bottom: 1px solid #e5e7eb; position: sticky; top:0; z-index: 10; }
.site-header h1 { margin: 0; padding: 1rem 0; }
nav { display: flex; gap: .5rem; padding-bottom: 1rem; }
nav button { background: #eef2ff; border: 1px solid #c7d2fe; padding: .5rem .75rem; border-radius: .5rem; cursor: pointer; }
nav button.active { background: var(--brand); color: white; border-color: var(--brand); }
.view { display: none; padding: 1rem 0 2rem; }
.view.active { display: block; }
.card-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 1rem; }
.card { background: #fff; border: 1px solid #e5e7eb; border-radius: .75rem; padding: 1rem; box-shadow: 0 1px 2px rgba(0,0,0,.04); }
.card h3 { margin-top: 0; }
.badge { display: inline-block; background: #d1fae5; color: #065f46; padding: .125rem .5rem; border-radius: 999px; font-size: .75rem; }
.site-footer { border-top: 1px solid #e5e7eb; padding: 1rem 0; color: var(--muted); }

/* Responsive tweaks */
@media (max-width: 640px) {
  nav { flex-wrap: wrap; }
}
"""

    def _generate_vanilla_index_js(self, project_structure: ProjectStructure) -> str:
        """Generate vanilla JS with simple interactivity and sample content."""
        return """// Data
const BREEDS = [
  { name: 'Rhode Island Red', eggColor: 'Brown', temperament: 'Hardy, friendly' },
  { name: 'Plymouth Rock', eggColor: 'Brown', temperament: 'Docile, calm' },
  { name: 'Leghorn', eggColor: 'White', temperament: 'Active, prolific layer' },
  { name: 'Wyandotte', eggColor: 'Brown', temperament: 'Cold-hardy, good layer' },
  { name: 'Orpington', eggColor: 'Brown', temperament: 'Gentle, dual-purpose' },
];

const CARE_TIPS = [
  'Provide a clean, dry coop with good ventilation.',
  'Ensure constant access to fresh water and balanced feed.',
  'Protect from predators; lock the coop at night.',
  'Offer dust baths to help control mites.',
  'Clean the coop regularly and replace bedding.',
];

const FACTS = [
  'Chickens can remember over 100 different faces.',
  'Hens can lay eggs without a rooster (they just won\'t be fertilized).',
  'Chickens have excellent color vision and can dream during REM sleep.',
];

// DOM helpers
const qs = (sel) => document.querySelector(sel);
const el = (tag, attrs = {}, children = []) => {
  const node = document.createElement(tag);
  Object.entries(attrs).forEach(([k, v]) => node.setAttribute(k, v));
  children.forEach((c) => (typeof c === 'string' ? (node.textContent = c) : node.appendChild(c)));
  return node;
};

// Renderers
function renderBreeds() {
  const container = qs('#breeds-list');
  container.innerHTML = '';
  BREEDS.forEach((b) => {
    const card = el('div', { class: 'card' }, [
      el('h3', {}, [b.name]),
      el('p', {}, [`Egg color: ${b.eggColor}`]),
      el('span', { class: 'badge' }, [b.temperament]),
    ]);
    container.appendChild(card);
  });
}

function renderCare() {
  const ul = qs('#care-list');
  ul.innerHTML = CARE_TIPS.map((t) => `<li>${t}</li>`).join('');
}

function renderFacts() {
  const ul = qs('#facts-list');
  ul.innerHTML = FACTS.map((t) => `<li>${t}</li>`).join('');
}

// Tabs
function setActive(id) {
  document.querySelectorAll('.view').forEach((v) => v.classList.remove('active'));
  qs(`#view-${id}`).classList.add('active');
  document.querySelectorAll('nav button').forEach((b) => b.classList.remove('active'));
  qs(`#tab-${id}`).classList.add('active');
}

function wireTabs() {
  ['breeds', 'care', 'facts'].forEach((id) => {
    qs(`#tab-${id}`).addEventListener('click', () => setActive(id));
  });
}

// Init
document.addEventListener('DOMContentLoaded', () => {
  renderBreeds();
  renderCare();
  renderFacts();
  wireTabs();
});
"""

    def _generate_vanilla_component_js(self, file_info: ProjectFile, project_structure: ProjectStructure) -> str:
        """Generate a reusable vanilla JS component module (no framework)."""
        name = file_info.path.split('/')[-1].replace('.js', '').replace('-', ' ').title().replace(' ', '')
        return f"""/**
 * {file_info.path} - Reusable DOM component
 * Generated by AI Coding Orchestrator
 */

export function create{name}(options = {{ title: 'Title', subtitle: '', body: '' }}) {{
  const card = document.createElement('div');
  card.className = 'card';

  const h3 = document.createElement('h3');
  h3.textContent = options.title;
  card.appendChild(h3);

  if (options.subtitle) {{
    const small = document.createElement('div');
    small.className = 'badge';
    small.textContent = options.subtitle;
    card.appendChild(small);
  }}

  if (options.body) {{
    const p = document.createElement('p');
    p.textContent = options.body;
    card.appendChild(p);
  }}

  return card;
}}
"""
    
    def _generate_readme_content(self, project_structure: ProjectStructure) -> str:
        """Generate README.md content for the project."""
        # Safely get roadmap phases if available
        roadmap_phases = []
        if hasattr(project_structure, 'development_roadmap'):
            roadmap = project_structure.development_roadmap
            if hasattr(roadmap, 'phases'):
                roadmap_phases = roadmap.phases
        
        return f"""# {project_structure.project_name}

## 🚀 Project Overview

This project was generated by the AI Coding Orchestrator.

## 📁 Project Structure

```
{chr(10).join([f"  {file.path}" for file in project_structure.files])}
```

## 🛠️ Technology Stack

{chr(10).join([f"- **{category}**: {', '.join(deps)}" for category, deps in project_structure.dependencies.items()])}

## 🚀 Getting Started

1. Navigate to the project directory: `cd {project_structure.root_directory}`
2. Install dependencies (see specific instructions below)
3. Follow the development roadmap

## 📋 Development Roadmap

{chr(10).join([f"- **{phase.name}**: {phase.description} ({phase.estimated_time})" for phase in roadmap_phases]) if roadmap_phases else "- Project roadmap will be generated during development"}

## 📚 Dependencies

{chr(10).join([f"### {category.title()}" + chr(10) + chr(10).join([f"- {dep}" for dep in deps]) for category, deps in project_structure.dependencies.items()])}

## 🔧 Build & Run

{chr(10).join([f"- {step}" for step in project_structure.build_steps])}

## 📖 Run Instructions

{chr(10).join([f"- {instruction}" for instruction in project_structure.run_instructions])}

---
*Generated by AI Coding Orchestrator*
"""
    
    def _generate_gitignore_content(self, project_structure: ProjectStructure) -> str:
        """Generate .gitignore content for the project."""
        gitignore_content = [
            "# Dependencies",
            "node_modules/",
            "__pycache__/",
            "*.pyc",
            "*.pyo",
            "*.pyd",
            ".Python",
            "env/",
            "venv/",
            ".venv/",
            "ENV/",
            "env.bak/",
            "venv.bak/",
            "",
            "# Build outputs",
            "dist/",
            "build/",
            "*.egg-info/",
            "",
            "# Environment variables",
            ".env",
            ".env.local",
            ".env.development.local",
            ".env.test.local",
            ".env.production.local",
            "",
            "# IDE files",
            ".vscode/",
            ".idea/",
            "*.swp",
            "*.swo",
            "*~",
            "",
            "# OS files",
            ".DS_Store",
            "Thumbs.db",
            "",
            "# Logs",
            "*.log",
            "npm-debug.log*",
            "yarn-debug.log*",
            "yarn-error.log*",
            "",
            "# Runtime data",
            "pids",
            "*.pid",
            "*.seed",
            "*.pid.lock",
            "",
            "# Coverage directory used by tools like istanbul",
            "coverage/",
            ".nyc_output",
            "",
            "# Dependency directories",
            "jspm_packages/",
            "",
            "# Optional npm cache directory",
            ".npm",
            "",
            "# Optional REPL history",
            ".node_repl_history",
            "",
            "# Output of 'npm pack'",
            "*.tgz",
            "",
            "# Yarn Integrity file",
            ".yarn-integrity",
            "",
            "# dotenv environment variables file",
            ".env",
            "",
            "# parcel-bundler cache (https://parceljs.org/)",
            ".cache",
            ".parcel-cache",
            "",
            "# next.js build output",
            ".next",
            "",
            "# nuxt.js build output",
            ".nuxt",
            "",
            "# vuepress build output",
            ".vuepress/dist",
            "",
            "# Serverless directories",
            ".serverless",
            "",
            "# FuseBox cache",
            ".fusebox/",
            "",
            "# DynamoDB Local files",
            ".dynamodb/",
            "",
            "# TernJS port file",
            ".tern-port"
        ]
        
        return "\n".join(gitignore_content)
    
    def _generate_schema_sql(self, project_structure: ProjectStructure) -> str:
        """Generate PostgreSQL schema file."""
        return f"""-- {project_structure.project_name} Database Schema
-- Generated by AI Coding Orchestrator

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Todos table (example for todo apps)
CREATE TABLE todos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    completed BOOLEAN DEFAULT FALSE,
    priority INTEGER DEFAULT 1,
    due_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_todos_user_id ON todos(user_id);
CREATE INDEX idx_todos_completed ON todos(completed);
CREATE INDEX idx_todos_due_date ON todos(due_date);

-- Update timestamp trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_todos_updated_at BEFORE UPDATE ON todos
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
"""
    
    def _generate_database_py(self, project_structure: ProjectStructure) -> str:
        """Generate database connection file."""
        return f"""#!/usr/bin/env python3
\"\"\"
{project_structure.project_name} - Database Configuration
Generated by AI Coding Orchestrator
\"\"\"

import os
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql://username:password@localhost:5432/{project_structure.project_name.lower()}"
)

# Create engine
engine = create_engine(DATABASE_URL, echo=True)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

# Dependency to get database session
def get_db():
    \"\"\"Get database session.\"\"\"
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Create all tables
def create_tables():
    \"\"\"Create all database tables.\"\"\"
    Base.metadata.create_all(bind=engine)

if __name__ == "__main__":
    create_tables()
    print("✅ Database tables created successfully!")
"""
    
    def _generate_models_py(self, project_structure: ProjectStructure) -> str:
        """Generate SQLAlchemy models file."""
        return f"""#!/usr/bin/env python3
\"\"\"
{project_structure.project_name} - Database Models
Generated by AI Coding Orchestrator
\"\"\"

from sqlalchemy import Column, String, Text, Boolean, Integer, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
import uuid

class User(Base):
    \"\"\"User model.\"\"\"
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    todos = relationship("Todo", back_populates="user", cascade="all, delete-orphan")

class Todo(Base):
    \"\"\"Todo model.\"\"\"
    __tablename__ = "todos"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    completed = Column(Boolean, default=False)
    priority = Column(Integer, default=1)
    due_date = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="todos")
"""
    
    def _generate_routers_py(self, project_structure: ProjectStructure) -> str:
        """Generate FastAPI routers file."""
        return f"""#!/usr/bin/env python3
\"\"\"
{project_structure.project_name} - API Routers
Generated by AI Coding Orchestrator
\"\"\"

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from .database import get_db
from .models import User, Todo
from .schemas import TodoCreate, TodoUpdate, TodoResponse

router = APIRouter(prefix="/api/v1", tags=["todos"])

@router.get("/todos/", response_model=List[TodoResponse])
async def get_todos(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    \"\"\"Get all todos with pagination.\"\"\"
    todos = db.query(Todo).offset(skip).limit(limit).all()
    return todos

@router.post("/todos/", response_model=TodoResponse, status_code=status.HTTP_201_CREATED)
async def create_todo(
    todo: TodoCreate,
    db: Session = Depends(get_db)
):
    \"\"\"Create a new todo.\"\"\"
    db_todo = Todo(**todo.dict())
    db.add(db_todo)
    db.commit()
    db.refresh(db_todo)
    return db_todo

@router.get("/todos/{{todo_id}}", response_model=TodoResponse)
async def get_todo(todo_id: str, db: Session = Depends(get_db)):
    \"\"\"Get a specific todo by ID.\"\"\"
    todo = db.query(Todo).filter(Todo.id == todo_id).first()
    if not todo:
        raise HTTPException(status_code=404, detail="Todo not found")
    return todo

@router.put("/todos/{{todo_id}}", response_model=TodoResponse)
async def update_todo(
    todo_id: str,
    todo_update: TodoUpdate,
    db: Session = Depends(get_db)
):
    \"\"\"Update a todo.\"\"\"
    db_todo = db.query(Todo).filter(Todo.id == todo_id).first()
    if not db_todo:
        raise HTTPException(status_code=404, detail="Todo not found")
    
    for field, value in todo_update.dict(exclude_unset=True).items():
        setattr(db_todo, field, value)
    
    db.commit()
    db.refresh(db_todo)
    return db_todo

@router.delete("/todos/{{todo_id}}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_todo(todo_id: str, db: Session = Depends(get_db)):
    \"\"\"Delete a todo.\"\"\"
    db_todo = db.query(Todo).filter(Todo.id == todo_id).first()
    if not db_todo:
        raise HTTPException(status_code=404, detail="Todo not found")
    
    db.delete(db_todo)
    db.commit()
"""
    
    def _generate_config_py(self, project_structure: ProjectStructure) -> str:
        """Generate configuration file."""
        return f"""#!/usr/bin/env python3
\"\"\"
{project_structure.project_name} - Configuration
Generated by AI Coding Orchestrator
\"\"\"

import os
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    \"\"\"Application settings.\"\"\"
    
    # Application
    app_name: str = "{project_structure.project_name}"
    app_version: str = "1.0.0"
    debug: bool = os.getenv("DEBUG", "False").lower() == "true"
    
    # Database
    database_url: str = os.getenv(
        "DATABASE_URL",
        "postgresql://username:password@localhost:5432/{project_structure.project_name.lower()}"
    )
    
    # Security
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # CORS
    allowed_origins: list = ["http://localhost:3000", "http://localhost:8000"]
    
    class Config:
        env_file = ".env"

# Global settings instance
settings = Settings()
"""
    
    def _generate_utils_py(self, project_structure: ProjectStructure) -> str:
        """Generate utilities file."""
        return f"""#!/usr/bin/env python3
\"\"\"
{project_structure.project_name} - Utility Functions
Generated by AI Coding Orchestrator
\"\"\"

import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional

def hash_password(password: str) -> str:
    \"\"\"Hash a password using SHA-256.\"\"\"
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed: str) -> bool:
    \"\"\"Verify a password against its hash.\"\"\"
    return hash_password(password) == hashed

def generate_token() -> str:
    \"\"\"Generate a secure random token.\"\"\"
    return secrets.token_urlsafe(32)

def format_datetime(dt: datetime) -> str:
    \"\"\"Format datetime for display.\"\"\"
    return dt.strftime("%Y-%m-%d %H:%M:%S")

def is_expired(dt: datetime, hours: int = 24) -> bool:
    \"\"\"Check if a datetime is expired.\"\"\"
    return datetime.utcnow() > dt + timedelta(hours=hours)
"""
    
    def _generate_test_file(self, file_info: ProjectFile, project_structure: ProjectStructure) -> str:
        """Generate test file."""
        return f"""#!/usr/bin/env python3
\"\"\"
Tests for {file_info.path}
Generated by AI Coding Orchestrator
\"\"\"

import pytest
from unittest.mock import Mock, patch
from pathlib import Path

# Import the module to test
# from . import module_name

class Test{file_info.path.replace('test_', '').replace('.py', '').title()}:
    \"\"\"Test cases for {file_info.path.replace('test_', '').replace('.py', '')}.\"\"\"
    
    def setup_method(self):
        \"\"\"Set up test fixtures.\"\"\"
        pass
    
    def teardown_method(self):
        \"\"\"Clean up after tests.\"\"\"
        pass
    
    def test_example(self):
        \"\"\"Example test case.\"\"\"
        assert True
    
    # TODO: Add more test cases based on the actual functionality
"""
    
    def _generate_css_file(self, file_info: ProjectFile, project_structure: ProjectStructure) -> str:
        """Generate CSS file."""
        return f"""/* {file_info.path} - Generated by AI Coding Orchestrator */

/* Reset and base styles */
* {{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}}

body {{
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}}

/* Container */
.container {{
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}}

/* Header */
.header {{
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem 0;
    margin-bottom: 2rem;
}}

.header h1 {{
    color: #2c3e50;
    text-align: center;
}}

/* Main content */
.main {{
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}}

/* Footer */
.footer {{
    background-color: #2c3e50;
    color: #fff;
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
}}

/* Responsive design */
@media (max-width: 768px) {{
    .container {{
        padding: 0 15px;
    }}
    
    .header h1 {{
        font-size: 1.5rem;
    }}
}}
"""
    
    def _generate_markdown_file(self, file_info: ProjectFile, project_structure: ProjectStructure) -> str:
        """Generate markdown file."""
        return f"""# {file_info.path}

## Overview

{file_info.description}

## Generated by AI Coding Orchestrator

This file was automatically generated as part of the {project_structure.project_name} project.

## TODO

- [ ] Implement the functionality described above
- [ ] Add proper documentation
- [ ] Include examples and usage
- [ ] Add tests

## Related Files

- Check the main project README.md for overall project structure
- Refer to other generated files for implementation details

---
*Generated by AI Coding Orchestrator*
"""
    
    def _generate_intelligent_default(self, file_info: ProjectFile, project_structure: ProjectStructure) -> str:
        """Generate intelligent default content based on file type and description."""
        file_ext = Path(file_info.path).suffix.lower()
        
        if file_ext == '.py':
            return f"""#!/usr/bin/env python3
\"\"\"
{file_info.path} - {file_info.description}
Generated by AI Coding Orchestrator
\"\"\"

# TODO: Implement {file_info.description}
# This file was generated as part of the {project_structure.project_name} project

def main():
    \"\"\"Main function for {file_info.path}.\"\"\"
    print(f"🚀 {file_info.path} is running!")
    print("Generated by AI Coding Orchestrator")
    
    # TODO: Add your implementation here
    
if __name__ == "__main__":
    main()
"""
        elif file_ext == '.js':
            return f"""/**
 * {file_info.path} - {file_info.description}
 * Generated by AI Coding Orchestrator
 */

console.log('🚀 {file_info.path} is running!');
console.log('Generated by AI Coding Orchestrator');

// TODO: Implement {file_info.description}
// This file was generated as part of the {project_structure.project_name} project

function main() {{
    // TODO: Add your implementation here
    console.log('Implement {file_info.description}');
}}

// Initialize when ready
if (typeof document !== 'undefined') {{
    // Browser environment
    if (document.readyState === 'loading') {{
        document.addEventListener('DOMContentLoaded', main);
    }} else {{
        main();
    }}
}} else {{
    // Node.js environment
    main();
}}
"""
        elif file_ext == '.json':
            return f"""{{
  "name": "{file_info.path.replace('.json', '')}",
  "description": "{file_info.description}",
  "version": "1.0.0",
  "generated_by": "AI Coding Orchestrator",
  "project": "{project_structure.project_name}"
}}
"""
        else:
            return f"""# {file_info.path}

## Description

{file_info.description}

## Generated by AI Coding Orchestrator

This file was automatically generated as part of the {project_structure.project_name} project.

## TODO

- [ ] Implement the functionality described above
- [ ] Add proper documentation
- [ ] Include examples and usage

---
*Generated by AI Coding Orchestrator*
"""
    
    def _generate_package_json(self, project_structure: ProjectStructure) -> str:
        """Generate package.json for Node.js projects."""
        return f"""{{
  "name": "{project_structure.project_name.lower().replace(' ', '-')}",
  "version": "1.0.0",
  "description": "Generated by AI Coding Orchestrator",
  "main": "src/index.js",
  "scripts": {{
    "start": "node src/index.js",
    "dev": "nodemon src/index.js",
    "test": "jest",
    "build": "webpack --mode production"
  }},
  "dependencies": {{
    {chr(10).join([f'    "{dep}": "^1.0.0"' for dep in project_structure.dependencies.get("frontend", [])])}
  }},
  "devDependencies": {{
    "nodemon": "^2.0.22",
    "jest": "^29.5.0",
    "webpack": "^5.88.0",
    "webpack-cli": "^5.1.0"
  }},
  "keywords": ["ai-generated", "coding-orchestrator"],
  "author": "AI Coding Orchestrator",
  "license": "MIT"
}}
"""
    
    def _generate_requirements_txt(self, project_structure: ProjectStructure) -> str:
        """Generate requirements.txt for Python projects."""
        requirements = []
        for category, deps in project_structure.dependencies.items():
            if category == "backend":
                requirements.extend(deps)
        
        return f"""# {project_structure.project_name} - Python Dependencies
# Generated by AI Coding Orchestrator

{chr(10).join([f"{dep}>=1.0.0" for dep in requirements])}

# Development dependencies
pytest>=7.0.0
black>=23.0.0
flake8>=6.0.0
"""
    
    def _generate_main_py(self, project_structure: ProjectStructure) -> str:
        """Generate main.py for Python projects."""
        return f"""#!/usr/bin/env python3
\"\"\"
{project_structure.project_name} - Main Application
Generated by AI Coding Orchestrator

A production-ready Python application with proper error handling,
logging, configuration management, and modular structure.
\"\"\"

import os
import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class {project_structure.project_name}:
    \"\"\"Main application class for {project_structure.project_name}.\"\"\"
    
    def __init__(self):
        self.config = self._load_config()
        self.is_running = False
        logger.info("Initializing {project_structure.project_name}")
    
    def _load_config(self) -> Dict[str, Any]:
        \"\"\"Load application configuration.\"\"\"
        config = {{
            "app_name": "{project_structure.project_name}",
            "version": "1.0.0",
            "debug": os.getenv("DEBUG", "False").lower() == "true",
            "port": int(os.getenv("PORT", "8000")),
            "host": os.getenv("HOST", "0.0.0.0"),
            "database_url": os.getenv("DATABASE_URL", "sqlite:///app.db"),
            "secret_key": os.getenv("SECRET_KEY", "your-secret-key-here")
        }}
        
        logger.info(f"Configuration loaded: {{config['app_name']}} v{{config['version']}}")
        return config
    
    async def initialize(self) -> bool:
        \"\"\"Initialize the application.\"\"\"
        try:
            logger.info("Initializing application components...")
            
            # Initialize database connection
            await self._init_database()
            
            # Initialize other components
            await self._init_components()
            
            logger.info("Application initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize application: {{e}}")
            return False
    
    async def _init_database(self):
        \"\"\"Initialize database connection.\"\"\"
        try:
            # Database initialization logic would go here
            logger.info("Database connection initialized")
        except Exception as e:
            logger.error(f"Database initialization failed: {{e}}")
            raise
    
    async def _init_components(self):
        \"\"\"Initialize application components.\"\"\"
        try:
            # Component initialization logic would go here
            logger.info("Application components initialized")
        except Exception as e:
            logger.error(f"Component initialization failed: {{e}}")
            raise
    
    async def start(self):
        \"\"\"Start the application.\"\"\"
        try:
            logger.info("Starting {project_structure.project_name}...")
            self.is_running = True
            
            # Main application loop
            while self.is_running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Received shutdown signal")
        except Exception as e:
            logger.error(f"Application error: {{e}}")
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        \"\"\"Shutdown the application gracefully.\"\"\"
        logger.info("Shutting down {project_structure.project_name}...")
        self.is_running = False
        
        # Cleanup logic would go here
        logger.info("Application shutdown completed")

async def main():
    \"\"\"Main application entry point.\"\"\"
    app = {project_structure.project_name}()
    
    try:
        if await app.initialize():
            await app.start()
        else:
            logger.error("Failed to initialize application")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Application failed to start: {{e}}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\\n👋 Application interrupted by user")
    except Exception as e:
        print(f"\\n❌ Application failed: {{e}}")
        sys.exit(1)
"""
    
    def _generate_index_html(self, project_structure: ProjectStructure) -> str:
        """Generate index.html for web projects."""
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{project_structure.project_name}</title>
    <link rel="stylesheet" href="src/styles/main.css">
</head>
<body>
    <div id="root">
        <h1>🚀 {project_structure.project_name}</h1>
        <p>Generated by AI Coding Orchestrator</p>
        <div id="app"></div>
    </div>
    
    <script src="src/index.js"></script>
</body>
</html>
"""
    
    def _generate_app_js(self, project_structure: ProjectStructure) -> str:
        """Generate app.js for JavaScript projects."""
        return f"""/**
 * {project_structure.project_name} - Main Application Component
 * Generated by AI Coding Orchestrator
 * 
 * A production-ready React component with proper state management,
 * error boundaries, and modern React patterns.
 */

import React, {{ useState, useEffect, useCallback, useMemo }} from 'react';
import './App.css';

// Custom hooks
const useLocalStorage = (key, initialValue) => {{
    const [storedValue, setStoredValue] = useState(() => {{
        try {{
            const item = window.localStorage.getItem(key);
            return item ? JSON.parse(item) : initialValue;
        }} catch (error) {{
            console.error('Error reading from localStorage:', error);
            return initialValue;
        }}
    }});

    const setValue = (value) => {{
        try {{
            setStoredValue(value);
            window.localStorage.setItem(key, JSON.stringify(value));
        }} catch (error) {{
            console.error('Error writing to localStorage:', error);
        }}
    }};

    return [storedValue, setValue];
}};

// Error Boundary Component
class ErrorBoundary extends React.Component {{
    constructor(props) {{
        super(props);
        this.state = {{ hasError: false, error: null }};
    }}

    static getDerivedStateFromError(error) {{
        return {{ hasError: true, error }};
    }}

    componentDidCatch(error, errorInfo) {{
        console.error('Error caught by boundary:', error, errorInfo);
    }}

    render() {{
        if (this.state.hasError) {{
            return (
                <div className="error-boundary">
                    <h2>Something went wrong!</h2>
                    <p>Please refresh the page or contact support.</p>
                    <button onClick={{() => window.location.reload()}}>
                        Refresh Page
                    </button>
                </div>
            );
        }}

        return this.props.children;
    }}
}}

// Main App Component
function {project_structure.project_name}() {{
    const [count, setCount] = useState(0);
    const [theme, setTheme] = useLocalStorage('theme', 'light');
    const [isLoading, setIsLoading] = useState(false);

    // Memoized values
    const isDarkTheme = useMemo(() => theme === 'dark', [theme]);
    const themeStyles = useMemo(() => ({{
        backgroundColor: isDarkTheme ? '#1a1a1a' : '#ffffff',
        color: isDarkTheme ? '#ffffff' : '#000000'
    }}), [isDarkTheme]);

    // Event handlers
    const handleIncrement = useCallback(() => {{
        setCount(prev => prev + 1);
    }}, []);

    const handleDecrement = useCallback(() => {{
        setCount(prev => Math.max(0, prev - 1));
    }}, []);

    const handleReset = useCallback(() => {{
        setCount(0);
    }}, []);

    const toggleTheme = useCallback(() => {{
        setTheme(prev => prev === 'light' ? 'dark' : 'light');
    }}, [setTheme]);

    const handleAsyncOperation = useCallback(async () => {{
        setIsLoading(true);
        try {{
            // Simulate async operation
            await new Promise(resolve => setTimeout(resolve, 1000));
            setCount(prev => prev + 10);
        }} catch (error) {{
            console.error('Async operation failed:', error);
        }} finally {{
            setIsLoading(false);
        }}
    }}, []);

    // Effects
    useEffect(() => {{
        document.title = `{{count}} - {project_structure.project_name}`;
    }}, [count]);

    useEffect(() => {{
        document.body.style.backgroundColor = themeStyles.backgroundColor;
        document.body.style.color = themeStyles.color;
    }}, [themeStyles]);

    // Render methods
    const renderCounter = () => (
        <div className="counter-section">
            <h2>Counter: {{count}}</h2>
            <div className="counter-controls">
                <button onClick={{handleDecrement}} disabled={{count === 0}}>
                    -
                </button>
                <button onClick={{handleIncrement}}>
                    +
                </button>
                <button onClick={{handleReset}} disabled={{count === 0}}>
                    Reset
                </button>
            </div>
        </div>
    );

    const renderThemeToggle = () => (
        <div className="theme-section">
            <button onClick={{toggleTheme}} className="theme-toggle">
                Switch to {{isDarkTheme ? 'Light' : 'Dark'}} Theme
            </button>
        </div>
    );

    const renderAsyncButton = () => (
        <div className="async-section">
            <button 
                onClick={{handleAsyncOperation}} 
                disabled={{isLoading}}
                className="async-button"
            >
                {{isLoading ? 'Processing...' : 'Add 10 (Async)'}}
            </button>
        </div>
    );

    return (
        <ErrorBoundary>
            <div className="App" style={{themeStyles}}>
                <header className="App-header">
                    <h1>🚀 {{project_structure.project_name}}</h1>
                    <p>Generated by AI Coding Orchestrator</p>
                </header>

                <main className="App-main">
                    {{renderCounter()}}
                    {{renderThemeToggle()}}
                    {{renderAsyncButton()}}
                </main>

                <footer className="App-footer">
                    <p>Built with React • Generated by AI</p>
                </footer>
            </div>
        </ErrorBoundary>
    );
}}

export default {project_structure.project_name};
"""
