# AI Coding Orchestrator Configuration
# Copy this file to .env and fill in your API keys

# AI Provider API Keys
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# AI Provider Preferences
PREFERRED_AI_PROVIDER=auto  # auto, openai, gem<PERSON>, claude
FALLBACK_AI_PROVIDER=openai

# System Configuration
LOG_LEVEL=INFO
MAX_RETRIES=3
REQUEST_TIMEOUT=60
MAX_TOKENS=4000

# Learning System
ENABLE_LEARNING=true
LEARNING_DB_PATH=data/patterns.db
TEMPLATE_DIR=data/templates

# File Monitoring
WATCH_PATTERNS=*.py,*.js,*.ts,*.jsx,*.tsx,*.html,*.css
IGNORE_PATTERNS=__pycache__/*,node_modules/*,.git/*

# Cursor Integration
CURSOR_INTEGRATION_MODE=file_based  # file_based, clipboard, monitoring
INSTRUCTION_FILE_PATH=.cursor_instructions.md
