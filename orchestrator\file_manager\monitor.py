"""
File monitoring system for the AI Coding Orchestrator.
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Callable
from pathlib import Path
from dataclasses import dataclass

@dataclass
class FileChangeEvent:
    """Represents a file change event."""
    file_path: str
    event_type: str  # created, modified, deleted
    timestamp: float
    file_size: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None

class FileMonitor:
    """Monitors file system changes in the project directory."""
    
    def __init__(self, project_directory: str = None):
        self.project_directory = Path(project_directory) if project_directory else Path.cwd()
        self.observer = None
        self.is_monitoring = False
        self.change_handlers: List[Callable] = []
        self.file_history: Dict[str, List[FileChangeEvent]] = {}
        
    async def start_monitoring(self) -> bool:
        """Start monitoring file changes."""
        try:
            from watchdog.observers import Observer
            from watchdog.events import FileSystemEventHandler
            
            class ProjectFileHandler(FileSystemEventHandler):
                def __init__(self, monitor):
                    self.monitor = monitor
                
                def on_created(self, event):
                    if not event.is_directory:
                        asyncio.create_task(self.monitor._handle_file_event(event.src_path, "created"))
                
                def on_modified(self, event):
                    if not event.is_directory:
                        asyncio.create_task(self.monitor._handle_file_event(event.src_path, "modified"))
                
                def on_deleted(self, event):
                    if not event.is_directory:
                        asyncio.create_task(self.monitor._handle_file_event(event.src_path, "deleted"))
                
                def on_moved(self, event):
                    if not event.is_directory:
                        asyncio.create_task(self.monitor._handle_file_event(event.dest_path, "moved"))
            
            self.observer = Observer()
            event_handler = ProjectFileHandler(self)
            self.observer.schedule(event_handler, str(self.project_directory), recursive=True)
            self.observer.start()
            
            self.is_monitoring = True
            print(f"📁 Started monitoring: {self.project_directory}")
            return True
            
        except ImportError:
            print("⚠️ File monitoring requires watchdog package. Install with: pip install watchdog")
            return False
        except Exception as e:
            print(f"❌ Failed to start file monitoring: {e}")
            return False
    
    async def stop_monitoring(self) -> None:
        """Stop monitoring file changes."""
        if self.observer and self.is_monitoring:
            self.observer.stop()
            self.observer.join()
            self.is_monitoring = False
            print("📁 Stopped file monitoring")
    
    async def _handle_file_event(self, file_path: str, event_type: str) -> None:
        """Handle a file change event."""
        try:
            # Get file information
            file_path_obj = Path(file_path)
            file_size = None
            metadata = {}
            
            if file_path_obj.exists():
                stat = file_path_obj.stat()
                file_size = stat.st_size
                metadata = {
                    "modified_time": stat.st_mtime,
                    "created_time": stat.st_ctime,
                    "is_file": file_path_obj.is_file(),
                    "is_dir": file_path_obj.is_dir()
                }
            
            # Create event
            event = FileChangeEvent(
                file_path=file_path,
                event_type=event_type,
                timestamp=time.time(),
                file_size=file_size,
                metadata=metadata
            )
            
            # Store in history
            if file_path not in self.file_history:
                self.file_history[file_path] = []
            self.file_history[file_path].append(event)
            
            # Limit history per file
            if len(self.file_history[file_path]) > 10:
                self.file_history[file_path] = self.file_history[file_path][-10:]
            
            # Notify handlers
            await self._notify_handlers(event)
            
            # Log event
            print(f"📁 File {event_type}: {file_path}")
            
        except Exception as e:
            print(f"Error handling file event: {e}")
    
    async def _notify_handlers(self, event: FileChangeEvent) -> None:
        """Notify all registered change handlers."""
        for handler in self.change_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event)
                else:
                    handler(event)
            except Exception as e:
                print(f"Error in file change handler: {e}")
    
    def add_change_handler(self, handler: Callable) -> None:
        """Add a file change handler."""
        self.change_handlers.append(handler)
    
    def remove_change_handler(self, handler: Callable) -> None:
        """Remove a file change handler."""
        if handler in self.change_handlers:
            self.change_handlers.remove(handler)
    
    def get_file_history(self, file_path: str = None) -> Dict[str, List[FileChangeEvent]]:
        """Get file change history."""
        if file_path:
            return {file_path: self.file_history.get(file_path, [])}
        return self.file_history.copy()
    
    def get_recent_changes(self, limit: int = 10) -> List[FileChangeEvent]:
        """Get recent file changes across all files."""
        all_events = []
        for events in self.file_history.values():
            all_events.extend(events)
        
        # Sort by timestamp and return recent ones
        all_events.sort(key=lambda x: x.timestamp, reverse=True)
        return all_events[:limit]
    
    def get_file_statistics(self) -> Dict[str, Any]:
        """Get statistics about file monitoring."""
        total_files = len(self.file_history)
        total_events = sum(len(events) for events in self.file_history.values())
        
        event_types = {}
        for events in self.file_history.values():
            for event in events:
                event_types[event.event_type] = event_types.get(event.event_type, 0) + 1
        
        return {
            "total_files_monitored": total_files,
            "total_events": total_events,
            "event_type_distribution": event_types,
            "is_monitoring": self.is_monitoring,
            "project_directory": str(self.project_directory)
        }
    
    async def scan_project_structure(self) -> Dict[str, Any]:
        """Scan the current project structure."""
        structure = {
            "root_directory": str(self.project_directory),
            "files": [],
            "directories": [],
            "total_size": 0,
            "file_types": {},
            "last_scan": time.time()
        }
        
        try:
            for item in self.project_directory.rglob("*"):
                if item.is_file():
                    # File information
                    stat = item.stat()
                    file_info = {
                        "path": str(item.relative_to(self.project_directory)),
                        "size": stat.st_size,
                        "modified": stat.st_mtime,
                        "extension": item.suffix.lower()
                    }
                    structure["files"].append(file_info)
                    structure["total_size"] += stat.st_size
                    
                    # Count file types
                    ext = item.suffix.lower()
                    if ext:
                        structure["file_types"][ext] = structure["file_types"].get(ext, 0) + 1
                    else:
                        structure["file_types"]["no_extension"] = structure["file_types"].get("no_extension", 0) + 1
                        
                elif item.is_dir():
                    # Directory information
                    dir_info = {
                        "path": str(item.relative_to(self.project_directory)),
                        "created": item.stat().st_ctime
                    }
                    structure["directories"].append(dir_info)
            
            print(f"📁 Project structure scanned: {len(structure['files'])} files, {len(structure['directories'])} directories")
            return structure
            
        except Exception as e:
            print(f"Error scanning project structure: {e}")
            return structure
    
    def is_file_being_modified(self, file_path: str, threshold_seconds: int = 5) -> bool:
        """Check if a file is currently being modified."""
        if file_path not in self.file_history:
            return False
        
        recent_events = self.file_history[file_path]
        if not recent_events:
            return False
        
        # Check if there was a recent modification
        latest_event = max(recent_events, key=lambda x: x.timestamp)
        time_since_last_event = time.time() - latest_event.timestamp
        
        return latest_event.event_type == "modified" and time_since_last_event < threshold_seconds
    
    def get_file_change_frequency(self, file_path: str, time_window_hours: int = 24) -> float:
        """Get the frequency of changes for a specific file."""
        if file_path not in self.file_history:
            return 0.0
        
        events = self.file_history[file_path]
        if not events:
            return 0.0
        
        # Filter events within time window
        cutoff_time = time.time() - (time_window_hours * 3600)
        recent_events = [e for e in events if e.timestamp > cutoff_time]
        
        if not recent_events:
            return 0.0
        
        # Calculate frequency (changes per hour)
        time_span_hours = time_window_hours
        return len(recent_events) / time_span_hours
    
    def __str__(self) -> str:
        return f"FileMonitor(monitoring={self.is_monitoring}, directory={self.project_directory})"
    
    def __repr__(self) -> str:
        return self.__str__()
