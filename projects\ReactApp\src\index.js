// Data
const BREEDS = [
  { name: 'Rhode Island Red', eggColor: '<PERSON>', temperament: '<PERSON>, friendly' },
  { name: 'Plymouth Rock', eggColor: '<PERSON>', temperament: 'Docile, calm' },
  { name: '<PERSON><PERSON><PERSON>', eggColor: '<PERSON>', temperament: 'Active, prolific layer' },
  { name: '<PERSON><PERSON><PERSON><PERSON>', eggColor: '<PERSON>', temperament: 'Cold-hardy, good layer' },
  { name: '<PERSON><PERSON><PERSON>', eggColor: 'Brown', temperament: 'Gentle, dual-purpose' },
];

const CARE_TIPS = [
  'Provide a clean, dry coop with good ventilation.',
  'Ensure constant access to fresh water and balanced feed.',
  'Protect from predators; lock the coop at night.',
  'Offer dust baths to help control mites.',
  'Clean the coop regularly and replace bedding.',
];

const FACTS = [
  'Chickens can remember over 100 different faces.',
  'Hens can lay eggs without a rooster (they just won't be fertilized).',
  'Chickens have excellent color vision and can dream during REM sleep.',
];

// DOM helpers
const qs = (sel) => document.querySelector(sel);
const el = (tag, attrs = {}, children = []) => {
  const node = document.createElement(tag);
  Object.entries(attrs).forEach(([k, v]) => node.setAttribute(k, v));
  children.forEach((c) => (typeof c === 'string' ? (node.textContent = c) : node.appendChild(c)));
  return node;
};

// Renderers
function renderBreeds() {
  const container = qs('#breeds-list');
  container.innerHTML = '';
  BREEDS.forEach((b) => {
    const card = el('div', { class: 'card' }, [
      el('h3', {}, [b.name]),
      el('p', {}, [`Egg color: ${b.eggColor}`]),
      el('span', { class: 'badge' }, [b.temperament]),
    ]);
    container.appendChild(card);
  });
}

function renderCare() {
  const ul = qs('#care-list');
  ul.innerHTML = CARE_TIPS.map((t) => `<li>${t}</li>`).join('');
}

function renderFacts() {
  const ul = qs('#facts-list');
  ul.innerHTML = FACTS.map((t) => `<li>${t}</li>`).join('');
}

// Tabs
function setActive(id) {
  document.querySelectorAll('.view').forEach((v) => v.classList.remove('active'));
  qs(`#view-${id}`).classList.add('active');
  document.querySelectorAll('nav button').forEach((b) => b.classList.remove('active'));
  qs(`#tab-${id}`).classList.add('active');
}

function wireTabs() {
  ['breeds', 'care', 'facts'].forEach((id) => {
    qs(`#tab-${id}`).addEventListener('click', () => setActive(id));
  });
}

// Init
document.addEventListener('DOMContentLoaded', () => {
  renderBreeds();
  renderCare();
  renderFacts();
  wireTabs();
});
