"""
Request Analyzer for deep analysis of user coding requests.
"""

import re
import json
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from ..ai_providers.base_provider import TaskType, AIRequest
from ..ai_providers.provider_manager import AIProviderManager

@dataclass
class TechnologyComponent:
    """Represents a technology component identified in the request."""
    name: str
    category: str  # frontend, backend, database, deployment, etc.
    confidence: float  # 0.0 to 1.0
    alternatives: List[str]
    description: str

@dataclass
class RequestAnalysis:
    """Complete analysis of a user request."""
    original_request: str
    identified_components: List[TechnologyComponent]
    complexity_level: str  # simple, medium, complex, enterprise
    estimated_time: str  # rough time estimate
    risk_factors: List[str]
    follow_up_questions: List[str]
    technology_stack: Dict[str, str]
    project_type: str
    requirements: List[str]
    constraints: List[str]
    # Enhanced analysis fields
    confidence_score: float  # Overall confidence in analysis
    ambiguity_level: str  # low, medium, high
    missing_information: List[str]  # What info is missing
    suggested_clarifications: List[str]  # Specific clarification questions
    architectural_patterns: List[str]  # Identified patterns (MVC, microservices, etc.)
    scalability_requirements: str  # Expected scale
    security_considerations: List[str]  # Security requirements
    integration_points: List[str]  # External integrations needed

class RequestAnalyzer:
    """Deep analyzer for user coding requests."""
    
    def __init__(self, ai_provider_manager: AIProviderManager):
        self.ai_provider_manager = ai_provider_manager

        # Enhanced technology patterns with context and confidence weights
        self.tech_patterns = {
            "frontend": {
                "react": {
                    "keywords": ["react", "jsx", "tsx", "next.js", "gatsby", "create-react-app"],
                    "context_boost": ["spa", "single page", "component", "hook", "state management"],
                    "confidence_weight": 0.9
                },
                "vue": {
                    "keywords": ["vue", "nuxt", "vuex", "vue.js"],
                    "context_boost": ["spa", "single page", "component", "reactive"],
                    "confidence_weight": 0.9
                },
                "angular": {
                    "keywords": ["angular", "ng", "typescript", "angular cli"],
                    "context_boost": ["spa", "enterprise", "typescript", "component"],
                    "confidence_weight": 0.9
                },
                "vanilla": {
                    "keywords": ["vanilla", "html", "css", "javascript", "js", "dom"],
                    "context_boost": ["simple", "basic", "no framework"],
                    "confidence_weight": 0.6
                }
            },
            "backend": {
                "python": {
                    "keywords": ["python", "django", "flask", "fastapi", "pyramid", "py"],
                    "context_boost": ["api", "rest", "web service", "machine learning", "data"],
                    "confidence_weight": 0.9
                },
                "node": {
                    "keywords": ["node", "express", "koa", "nest", "javascript", "js", "npm"],
                    "context_boost": ["api", "rest", "real-time", "websocket", "microservice"],
                    "confidence_weight": 0.9
                },
                "java": {
                    "keywords": ["java", "spring", "jakarta", "maven", "gradle"],
                    "context_boost": ["enterprise", "microservice", "api", "rest"],
                    "confidence_weight": 0.9
                },
                "csharp": {
                    "keywords": ["c#", "csharp", "asp.net", "dotnet", ".net", "core"],
                    "context_boost": ["enterprise", "api", "rest", "microsoft"],
                    "confidence_weight": 0.9
                },
                "go": {
                    "keywords": ["go", "golang", "gin", "echo", "fiber"],
                    "context_boost": ["microservice", "api", "performance", "concurrent"],
                    "confidence_weight": 0.8
                },
                "rust": {
                    "keywords": ["rust", "actix", "rocket", "axum", "warp"],
                    "context_boost": ["performance", "system", "safe", "concurrent"],
                    "confidence_weight": 0.8
                }
            },
            "database": {
                "postgresql": {
                    "keywords": ["postgres", "postgresql", "psql", "pg"],
                    "context_boost": ["relational", "sql", "acid", "enterprise"],
                    "confidence_weight": 0.9
                },
                "mysql": {
                    "keywords": ["mysql", "mariadb"],
                    "context_boost": ["relational", "sql", "web"],
                    "confidence_weight": 0.9
                },
                "mongodb": {
                    "keywords": ["mongo", "mongodb", "nosql", "document"],
                    "context_boost": ["nosql", "document", "json", "flexible"],
                    "confidence_weight": 0.9
                },
                "sqlite": {
                    "keywords": ["sqlite", "sqlite3"],
                    "context_boost": ["simple", "embedded", "local", "file"],
                    "confidence_weight": 0.7
                },
                "redis": {
                    "keywords": ["redis", "cache", "session", "pub/sub"],
                    "context_boost": ["cache", "session", "real-time", "memory"],
                    "confidence_weight": 0.8
                }
            },
            "deployment": {
                "docker": {
                    "keywords": ["docker", "container", "kubernetes", "k8s", "containerize"],
                    "context_boost": ["deploy", "scale", "microservice", "orchestration"],
                    "confidence_weight": 0.9
                },
                "aws": {
                    "keywords": ["aws", "amazon", "ec2", "lambda", "s3", "rds"],
                    "context_boost": ["cloud", "scale", "serverless", "infrastructure"],
                    "confidence_weight": 0.9
                },
                "azure": {
                    "keywords": ["azure", "microsoft", "app service"],
                    "context_boost": ["cloud", "microsoft", "enterprise"],
                    "confidence_weight": 0.8
                },
                "gcp": {
                    "keywords": ["gcp", "google", "cloud", "app engine", "firebase"],
                    "context_boost": ["cloud", "google", "firebase", "serverless"],
                    "confidence_weight": 0.8
                },
                "heroku": {
                    "keywords": ["heroku", "paas"],
                    "context_boost": ["simple", "deploy", "paas"],
                    "confidence_weight": 0.7
                }
            }
        }

        # Architectural patterns recognition
        self.architecture_patterns = {
            "mvc": ["mvc", "model view controller", "model-view-controller"],
            "microservices": ["microservice", "micro service", "service oriented", "soa"],
            "monolith": ["monolith", "monolithic", "single application"],
            "serverless": ["serverless", "lambda", "function as a service", "faas"],
            "event_driven": ["event driven", "event-driven", "pub/sub", "message queue"],
            "rest_api": ["rest", "restful", "api", "http api"],
            "graphql": ["graphql", "graph ql", "query language"],
            "spa": ["spa", "single page application", "single-page"],
            "pwa": ["pwa", "progressive web app", "progressive web application"],
            "jamstack": ["jamstack", "jam stack", "static site", "headless"]
        }

        # Complexity indicators
        self.complexity_indicators = {
            "simple": ["simple", "basic", "small", "prototype", "demo", "learning"],
            "medium": ["medium", "standard", "typical", "production", "business"],
            "complex": ["complex", "advanced", "enterprise", "large scale", "distributed"],
            "enterprise": ["enterprise", "mission critical", "high availability", "scalable", "fault tolerant"]
        }
    
    async def analyze_request(self, user_request: str) -> RequestAnalysis:
        """Perform deep analysis of the user request."""
        print(f"🔍 Analyzing request: {user_request[:100]}...")
        
        # Initial pattern-based analysis
        initial_analysis = self._pattern_analysis(user_request)
        
        # AI-powered deep analysis
        ai_analysis = await self._ai_analysis(user_request, initial_analysis)
        
        # Combine and refine analysis
        final_analysis = self._combine_analyses(user_request, initial_analysis, ai_analysis)
        
        return final_analysis
    
    def _pattern_analysis(self, request: str) -> Dict[str, Any]:
        """Perform enhanced pattern-based analysis of the request."""
        request_lower = request.lower()

        identified_components = []
        technology_stack = {}
        architectural_patterns = []

        # Identify technologies by category with enhanced confidence scoring
        for category, techs in self.tech_patterns.items():
            best_match = None
            best_confidence = 0.0

            for tech_name, tech_info in techs.items():
                confidence = self._calculate_enhanced_confidence(request_lower, tech_info)

                if confidence > 0.3 and confidence > best_confidence:  # Minimum confidence threshold
                    best_match = (tech_name, confidence)
                    best_confidence = confidence

            if best_match:
                tech_name, confidence = best_match
                component = TechnologyComponent(
                    name=tech_name,
                    category=category,
                    confidence=confidence,
                    alternatives=self._get_alternatives(category, tech_name),
                    description=self._get_tech_description(tech_name)
                )
                identified_components.append(component)
                technology_stack[category] = tech_name

        # Identify architectural patterns
        for pattern_name, keywords in self.architecture_patterns.items():
            for keyword in keywords:
                if keyword in request_lower:
                    architectural_patterns.append(pattern_name)
                    break

        # Determine project type with more sophistication
        project_type = self._identify_project_type_enhanced(request_lower, architectural_patterns)

        # Estimate complexity with multiple factors
        complexity = self._estimate_complexity_enhanced(request_lower, len(identified_components), architectural_patterns)

        # Identify missing information and ambiguities
        missing_info = self._identify_missing_information(request_lower, technology_stack)
        ambiguity_level = self._assess_ambiguity(request_lower, technology_stack)

        return {
            "components": identified_components,
            "technology_stack": technology_stack,
            "project_type": project_type,
            "complexity": complexity,
            "architectural_patterns": architectural_patterns,
            "missing_information": missing_info,
            "ambiguity_level": ambiguity_level
        }
    
    async def _ai_analysis(self, request: str, pattern_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Use AI to perform deep analysis of the request."""
        try:
            # Create AI prompt for analysis
            prompt = self._create_analysis_prompt(request, pattern_analysis)
            
            ai_request = AIRequest(
                prompt=prompt,
                task_type=TaskType.ANALYSIS,
                context={
                    "system_prompt": "You are an expert software architect and project planner. Analyze the user's coding request and provide detailed insights."
                }
            )
            
            response = await self.ai_provider_manager.generate_response(ai_request)
            
            # Parse AI response
            return self._parse_ai_analysis(response.content)
            
        except Exception as e:
            print(f"AI analysis failed: {e}")
            # Fallback so flow can continue with useful defaults
            return {
                "follow_up_questions": self._generate_default_questions(
                    pattern_analysis.get("components", []),
                    pattern_analysis.get("technology_stack", {})
                ),
                "risk_factors": ["AI analysis unavailable or rate-limited"],
                "estimated_time": "2-4 weeks",
                "requirements": [],
                "constraints": []
            }
    
    def _create_analysis_prompt(self, request: str, pattern_analysis: Dict[str, Any]) -> str:
        """Create a comprehensive prompt for AI analysis."""
        components = [c.name for c in pattern_analysis.get('components', [])]
        tech_stack = pattern_analysis.get('technology_stack', {})
        arch_patterns = pattern_analysis.get('architectural_patterns', [])
        missing_info = pattern_analysis.get('missing_information', [])

        prompt = f"""You are an expert software architect and project planner. Analyze this coding request comprehensively.

REQUEST: "{request}"

INITIAL PATTERN ANALYSIS:
- Identified Technologies: {components}
- Technology Stack: {tech_stack}
- Architectural Patterns: {arch_patterns}
- Project Type: {pattern_analysis.get('project_type', 'unknown')}
- Initial Complexity: {pattern_analysis.get('complexity', 'unknown')}
- Missing Information: {missing_info}
- Ambiguity Level: {pattern_analysis.get('ambiguity_level', 'unknown')}

PROVIDE DETAILED ANALYSIS IN JSON FORMAT:
{{
    "refined_complexity": "simple|medium|complex|enterprise",
    "confidence_score": 0.0-1.0,
    "estimated_time": "specific time estimate",
    "missing_components": ["list of missing tech components"],
    "risk_factors": ["specific risks and challenges"],
    "follow_up_questions": ["critical clarification questions"],
    "requirements": ["functional and non-functional requirements"],
    "constraints": ["technical and business constraints"],
    "scalability_requirements": "expected scale and performance needs",
    "security_considerations": ["security requirements and concerns"],
    "integration_points": ["external systems and APIs needed"],
    "suggested_clarifications": ["specific questions to reduce ambiguity"],
    "architectural_recommendations": ["recommended patterns and approaches"],
    "technology_alternatives": {{"category": ["alternative options"]}},
    "development_phases": ["logical development phases"],
    "critical_decisions": ["key architectural decisions needed"]
}}

Focus on:
1. Identifying gaps in the initial analysis
2. Assessing technical complexity and risks
3. Generating intelligent follow-up questions
4. Providing actionable recommendations
5. Highlighting critical decisions needed

Be specific and practical in your analysis."""
        return prompt
    
    def _parse_ai_analysis(self, ai_response: str) -> Dict[str, Any]:
        """Parse the AI analysis response from JSON format."""
        try:
            # Try to extract JSON from the response
            json_start = ai_response.find('{')
            json_end = ai_response.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_str = ai_response[json_start:json_end]
                parsed_analysis = json.loads(json_str)

                # Validate and normalize the parsed analysis
                return {
                    "refined_complexity": parsed_analysis.get("refined_complexity", "medium"),
                    "confidence_score": float(parsed_analysis.get("confidence_score", 0.7)),
                    "estimated_time": parsed_analysis.get("estimated_time", "2-4 weeks"),
                    "missing_components": parsed_analysis.get("missing_components", []),
                    "risk_factors": parsed_analysis.get("risk_factors", []),
                    "follow_up_questions": parsed_analysis.get("follow_up_questions", []),
                    "requirements": parsed_analysis.get("requirements", []),
                    "constraints": parsed_analysis.get("constraints", []),
                    "scalability_requirements": parsed_analysis.get("scalability_requirements", "standard"),
                    "security_considerations": parsed_analysis.get("security_considerations", []),
                    "integration_points": parsed_analysis.get("integration_points", []),
                    "suggested_clarifications": parsed_analysis.get("suggested_clarifications", []),
                    "architectural_recommendations": parsed_analysis.get("architectural_recommendations", []),
                    "technology_alternatives": parsed_analysis.get("technology_alternatives", {}),
                    "development_phases": parsed_analysis.get("development_phases", []),
                    "critical_decisions": parsed_analysis.get("critical_decisions", [])
                }
            else:
                raise ValueError("No valid JSON found in response")

        except (json.JSONDecodeError, ValueError, KeyError) as e:
            print(f"Failed to parse AI analysis JSON: {e}")
            # Fallback to text parsing
            return self._parse_ai_analysis_fallback(ai_response)

    def _parse_ai_analysis_fallback(self, ai_response: str) -> Dict[str, Any]:
        """Fallback text parsing when JSON parsing fails."""
        analysis = {
            "follow_up_questions": [],
            "risk_factors": [],
            "estimated_time": "unknown",
            "requirements": [],
            "constraints": []
        }
        
        # Extract follow-up questions
        if "follow-up" in ai_response.lower() or "questions" in ai_response.lower():
            lines = ai_response.split('\n')
            for line in lines:
                if line.strip().startswith(('?', '-', '*', '1.', '2.', '3.')):
                    question = line.strip().lstrip('?*-123456789. ')
                    if question and len(question) > 10:
                        analysis["follow_up_questions"].append(question)
        
        # Extract risk factors
        if "risk" in ai_response.lower():
            lines = ai_response.split('\n')
            for line in lines:
                if any(word in line.lower() for word in ["risk", "challenge", "difficulty", "complex"]):
                    risk = line.strip().lstrip('*-123456789. ')
                    if risk and len(risk) > 10:
                        analysis["risk_factors"].append(risk)
        
        # Extract time estimation
        time_patterns = [
            r"(\d+)\s*(?:hours?|hrs?)",
            r"(\d+)\s*(?:days?)",
            r"(\d+)\s*(?:weeks?)",
            r"(\d+)\s*(?:months?)"
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, ai_response.lower())
            if match:
                analysis["estimated_time"] = match.group(0)
                break
        
        return analysis
    
    def _combine_analyses(self, request: str, pattern_analysis: Dict[str, Any], ai_analysis: Dict[str, Any]) -> RequestAnalysis:
        """Combine pattern and AI analysis into final result."""
        components = pattern_analysis.get("components", [])
        technology_stack = pattern_analysis.get("technology_stack", {})
        project_type = pattern_analysis.get("project_type", "web_application")
        complexity = ai_analysis.get("refined_complexity", pattern_analysis.get("complexity", "medium"))

        # Extract enhanced analysis data
        follow_up_questions = ai_analysis.get("follow_up_questions", [])
        risk_factors = ai_analysis.get("risk_factors", [])
        estimated_time = ai_analysis.get("estimated_time", "2-4 weeks")
        requirements = ai_analysis.get("requirements", [])
        constraints = ai_analysis.get("constraints", [])

        # Enhanced fields from AI analysis
        confidence_score = ai_analysis.get("confidence_score", 0.7)
        ambiguity_level = pattern_analysis.get("ambiguity_level", "medium")
        missing_information = pattern_analysis.get("missing_information", [])
        suggested_clarifications = ai_analysis.get("suggested_clarifications", [])
        architectural_patterns = pattern_analysis.get("architectural_patterns", [])
        scalability_requirements = ai_analysis.get("scalability_requirements", "standard")
        security_considerations = ai_analysis.get("security_considerations", [])
        integration_points = ai_analysis.get("integration_points", [])

        # Add default follow-up questions if AI didn't provide any
        if not follow_up_questions:
            follow_up_questions = self._generate_default_questions(components, technology_stack)

        # Combine suggested clarifications with follow-up questions
        all_questions = follow_up_questions + suggested_clarifications
        unique_questions = list(dict.fromkeys(all_questions))  # Remove duplicates while preserving order

        return RequestAnalysis(
            original_request=request,
            identified_components=components,
            complexity_level=complexity,
            estimated_time=estimated_time,
            risk_factors=risk_factors,
            follow_up_questions=unique_questions[:8],  # Limit to 8 questions
            technology_stack=technology_stack,
            project_type=project_type,
            requirements=requirements,
            constraints=constraints,
            # Enhanced fields
            confidence_score=confidence_score,
            ambiguity_level=ambiguity_level,
            missing_information=missing_information,
            suggested_clarifications=suggested_clarifications[:5],  # Limit to 5
            architectural_patterns=architectural_patterns,
            scalability_requirements=scalability_requirements,
            security_considerations=security_considerations,
            integration_points=integration_points
        )
    
    def _calculate_confidence(self, request: str, pattern: str, tech_name: str) -> float:
        """Calculate confidence level for a technology identification."""
        base_confidence = 0.5
        
        # Higher confidence for exact matches
        if tech_name in request:
            base_confidence += 0.3
        
        # Higher confidence for specific mentions
        if pattern in request:
            base_confidence += 0.2
        
        # Context-based adjustments
        if any(word in request for word in ["build", "create", "develop", "make"]):
            base_confidence += 0.1
        
        return min(base_confidence, 1.0)
    
    def _get_alternatives(self, category: str, tech_name: str) -> List[str]:
        """Get alternative technologies for a category."""
        alternatives = []
        if category in self.tech_patterns:
            alternatives = list(self.tech_patterns[category].keys())
            if tech_name in alternatives:
                alternatives.remove(tech_name)
        return alternatives[:3]  # Limit to top 3 alternatives
    
    def _get_tech_description(self, tech_name: str) -> str:
        """Get description for a technology."""
        descriptions = {
            "react": "Modern JavaScript library for building user interfaces",
            "vue": "Progressive JavaScript framework for building UIs",
            "angular": "Platform for building mobile and desktop web applications",
            "python": "High-level programming language with extensive libraries",
            "fastapi": "Modern, fast web framework for building APIs with Python",
            "postgresql": "Advanced open-source relational database",
            "mongodb": "Document-oriented NoSQL database",
            "docker": "Platform for developing, shipping, and running applications"
        }
        return descriptions.get(tech_name, f"{tech_name} technology")
    
    def _identify_project_type(self, request: str) -> str:
        """Identify the type of project from the request."""
        request_lower = request.lower()
        
        if any(word in request_lower for word in ["fullstack", "full-stack", "full stack"]):
            return "fullstack_application"
        if any(word in request_lower for word in ["api", "backend", "server"]):
            return "backend_api"
        elif any(word in request_lower for word in ["frontend", "ui", "interface", "web app", "spa", "website"]):
            return "frontend_application"
        elif any(word in request_lower for word in ["mobile", "ios", "android", "react native", "flutter"]):
            return "mobile_application"
        elif any(word in request_lower for word in ["desktop", "gui", "application"]):
            return "desktop_application"
        else:
            return "web_application"
    
    def _estimate_complexity(self, request: str, component_count: int) -> str:
        """Estimate project complexity."""
        complexity_score = 0
        
        # Component count factor
        complexity_score += component_count * 0.3
        
        # Request length factor
        complexity_score += len(request.split()) * 0.01
        
        # Feature complexity indicators
        complex_features = ["authentication", "database", "api", "deployment", "testing", "security"]
        for feature in complex_features:
            if feature in request.lower():
                complexity_score += 0.2
        
        if complexity_score < 1.0:
            return "simple"
        elif complexity_score < 2.5:
            return "medium"
        elif complexity_score < 4.0:
            return "complex"
        else:
            return "enterprise"
    
    def _generate_default_questions(self, components: List[TechnologyComponent], tech_stack: Dict[str, str]) -> List[str]:
        """Generate default follow-up questions based on identified components."""
        questions = []
        
        # Frontend questions
        if "frontend" not in tech_stack:
            questions.append("What type of user interface do you need? (web app, mobile app, desktop app)")
        
        # Backend questions
        if "backend" not in tech_stack:
            questions.append("Do you need a backend API or server-side logic?")
        
        # Database questions
        if "database" not in tech_stack:
            questions.append("Do you need to store data? What type of data will you be working with?")
        
        # Deployment questions
        if "deployment" not in tech_stack:
            questions.append("Where do you plan to deploy this application? (cloud, local, specific platform)")
        
        # Authentication questions
        if any(word in str(components).lower() for word in ["user", "login", "account"]):
            questions.append("Do you need user authentication and user management?")
        
        return questions[:5]  # Limit to 5 questions

    def _calculate_enhanced_confidence(self, request: str, tech_info: Dict[str, Any]) -> float:
        """Calculate enhanced confidence score using keywords and context."""
        confidence = 0.0
        base_weight = tech_info.get("confidence_weight", 0.5)

        # Check for direct keyword matches
        keyword_matches = 0
        for keyword in tech_info.get("keywords", []):
            if keyword in request:
                keyword_matches += 1
                confidence += 0.2

        # Boost confidence based on context
        context_boost = 0.0
        for context_word in tech_info.get("context_boost", []):
            if context_word in request:
                context_boost += 0.1

        # Apply base weight and context boost
        final_confidence = min((confidence + context_boost) * base_weight, 1.0)

        # Minimum confidence for any match
        if keyword_matches > 0:
            final_confidence = max(final_confidence, 0.3)

        return final_confidence

    def _identify_project_type_enhanced(self, request: str, architectural_patterns: List[str]) -> str:
        """Enhanced project type identification using patterns."""
        request_lower = request.lower()

        # Check architectural patterns first
        if "microservices" in architectural_patterns:
            return "microservices_architecture"
        if "spa" in architectural_patterns:
            return "single_page_application"
        if "pwa" in architectural_patterns:
            return "progressive_web_app"
        if "jamstack" in architectural_patterns:
            return "jamstack_site"

        # Traditional type detection
        if any(word in request_lower for word in ["fullstack", "full-stack", "full stack"]):
            return "fullstack_application"
        if any(word in request_lower for word in ["api", "backend", "server", "microservice"]):
            return "backend_api"
        if any(word in request_lower for word in ["frontend", "ui", "interface", "website"]):
            return "frontend_application"
        if any(word in request_lower for word in ["mobile", "app", "ios", "android"]):
            return "mobile_application"
        if any(word in request_lower for word in ["desktop", "electron", "gui"]):
            return "desktop_application"
        if any(word in request_lower for word in ["cli", "command line", "tool", "script"]):
            return "command_line_tool"
        if any(word in request_lower for word in ["data", "analytics", "ml", "machine learning"]):
            return "data_application"

        return "web_application"

    def _estimate_complexity_enhanced(self, request: str, component_count: int, architectural_patterns: List[str]) -> str:
        """Enhanced complexity estimation using multiple factors."""
        complexity_score = 0

        # Base complexity from component count
        if component_count <= 2:
            complexity_score += 1
        elif component_count <= 4:
            complexity_score += 2
        else:
            complexity_score += 3

        # Complexity from architectural patterns
        complex_patterns = ["microservices", "event_driven", "serverless"]
        if any(pattern in architectural_patterns for pattern in complex_patterns):
            complexity_score += 2

        # Complexity indicators from text
        for complexity_level, indicators in self.complexity_indicators.items():
            for indicator in indicators:
                if indicator in request.lower():
                    if complexity_level == "simple":
                        complexity_score += 0
                    elif complexity_level == "medium":
                        complexity_score += 1
                    elif complexity_level == "complex":
                        complexity_score += 2
                    elif complexity_level == "enterprise":
                        complexity_score += 3
                    break

        # Additional complexity factors
        if any(word in request.lower() for word in ["authentication", "auth", "login", "user management"]):
            complexity_score += 1
        if any(word in request.lower() for word in ["payment", "billing", "subscription"]):
            complexity_score += 2
        if any(word in request.lower() for word in ["real-time", "websocket", "live", "streaming"]):
            complexity_score += 1
        if any(word in request.lower() for word in ["scale", "scalable", "high traffic", "performance"]):
            complexity_score += 2

        # Map score to complexity level
        if complexity_score <= 2:
            return "simple"
        elif complexity_score <= 4:
            return "medium"
        elif complexity_score <= 6:
            return "complex"
        else:
            return "enterprise"

    def _identify_missing_information(self, request: str, tech_stack: Dict[str, str]) -> List[str]:
        """Identify what information is missing from the request."""
        missing = []

        if not tech_stack.get("database"):
            missing.append("Database technology not specified")
        if not tech_stack.get("deployment"):
            missing.append("Deployment strategy not mentioned")

        # Check for common missing elements
        if "auth" not in request.lower() and "login" not in request.lower():
            missing.append("Authentication requirements unclear")
        if "user" not in request.lower() and "customer" not in request.lower():
            missing.append("Target user base not defined")
        if not any(word in request.lower() for word in ["scale", "performance", "load"]):
            missing.append("Performance and scalability requirements not specified")
        if not any(word in request.lower() for word in ["security", "secure", "privacy"]):
            missing.append("Security requirements not mentioned")

        return missing

    def _assess_ambiguity(self, request: str, tech_stack: Dict[str, str]) -> str:
        """Assess the level of ambiguity in the request."""
        ambiguity_score = 0

        # Vague terms increase ambiguity
        vague_terms = ["simple", "basic", "good", "nice", "modern", "clean", "professional"]
        for term in vague_terms:
            if term in request.lower():
                ambiguity_score += 1

        # Missing specifics increase ambiguity
        if len(tech_stack) < 2:
            ambiguity_score += 2

        # Short requests are often ambiguous
        if len(request.split()) < 10:
            ambiguity_score += 1

        # Lack of requirements increases ambiguity
        if not any(word in request.lower() for word in ["need", "require", "must", "should", "want"]):
            ambiguity_score += 1

        if ambiguity_score <= 1:
            return "low"
        elif ambiguity_score <= 3:
            return "medium"
        else:
            return "high"
