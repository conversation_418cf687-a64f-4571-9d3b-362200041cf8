"""
Request Analyzer for deep analysis of user coding requests.
"""

import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from ..ai_providers.base_provider import TaskType, AIRequest
from ..ai_providers.provider_manager import AIProviderManager

@dataclass
class TechnologyComponent:
    """Represents a technology component identified in the request."""
    name: str
    category: str  # frontend, backend, database, deployment, etc.
    confidence: float  # 0.0 to 1.0
    alternatives: List[str]
    description: str

@dataclass
class RequestAnalysis:
    """Complete analysis of a user request."""
    original_request: str
    identified_components: List[TechnologyComponent]
    complexity_level: str  # simple, medium, complex, enterprise
    estimated_time: str  # rough time estimate
    risk_factors: List[str]
    follow_up_questions: List[str]
    technology_stack: Dict[str, str]
    project_type: str
    requirements: List[str]
    constraints: List[str]

class RequestAnalyzer:
    """Deep analyzer for user coding requests."""
    
    def __init__(self, ai_provider_manager: AIProviderManager):
        self.ai_provider_manager = ai_provider_manager
        
        # Common technology patterns
        self.tech_patterns = {
            "frontend": {
                "react": ["react", "jsx", "tsx", "next.js", "gatsby"],
                "vue": ["vue", "nuxt", "vuex"],
                "angular": ["angular", "ng"],
                "vanilla": ["vanilla", "html", "css", "javascript", "js"]
            },
            "backend": {
                "python": ["python", "django", "flask", "fastapi", "pyramid"],
                "node": ["node", "express", "koa", "nest", "javascript", "js"],
                "java": ["java", "spring", "jakarta", "maven"],
                "csharp": ["c#", "csharp", "asp.net", "dotnet", ".net"],
                "go": ["go", "golang", "gin", "echo"],
                "rust": ["rust", "actix", "rocket", "axum"]
            },
            "database": {
                "postgresql": ["postgres", "postgresql", "psql"],
                "mysql": ["mysql", "mariadb"],
                "mongodb": ["mongo", "mongodb", "nosql"],
                "sqlite": ["sqlite", "sqlite3"],
                "redis": ["redis", "cache"],
                "elasticsearch": ["elasticsearch", "elastic", "search"]
            },
            "deployment": {
                "docker": ["docker", "container", "kubernetes", "k8s"],
                "aws": ["aws", "amazon", "ec2", "lambda", "s3"],
                "azure": ["azure", "microsoft"],
                "gcp": ["gcp", "google", "cloud", "app engine"],
                "heroku": ["heroku", "paas"],
                "vercel": ["vercel", "deploy"]
            }
        }
    
    async def analyze_request(self, user_request: str) -> RequestAnalysis:
        """Perform deep analysis of the user request."""
        print(f"🔍 Analyzing request: {user_request[:100]}...")
        
        # Initial pattern-based analysis
        initial_analysis = self._pattern_analysis(user_request)
        
        # AI-powered deep analysis
        ai_analysis = await self._ai_analysis(user_request, initial_analysis)
        
        # Combine and refine analysis
        final_analysis = self._combine_analyses(user_request, initial_analysis, ai_analysis)
        
        return final_analysis
    
    def _pattern_analysis(self, request: str) -> Dict[str, Any]:
        """Perform pattern-based analysis of the request."""
        request_lower = request.lower()
        
        identified_components = []
        technology_stack = {}
        
        # Identify technologies by category
        for category, techs in self.tech_patterns.items():
            for tech_name, patterns in techs.items():
                for pattern in patterns:
                    if pattern in request_lower:
                        # Calculate confidence based on context
                        confidence = self._calculate_confidence(request_lower, pattern, tech_name)
                        
                        if confidence > 0.3:  # Minimum confidence threshold
                            component = TechnologyComponent(
                                name=tech_name,
                                category=category,
                                confidence=confidence,
                                alternatives=self._get_alternatives(category, tech_name),
                                description=self._get_tech_description(tech_name)
                            )
                            identified_components.append(component)
                            technology_stack[category] = tech_name
                            break
        
        # Determine project type
        project_type = self._identify_project_type(request_lower)
        
        # Estimate complexity
        complexity = self._estimate_complexity(request_lower, len(identified_components))
        
        return {
            "components": identified_components,
            "technology_stack": technology_stack,
            "project_type": project_type,
            "complexity": complexity
        }
    
    async def _ai_analysis(self, request: str, pattern_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Use AI to perform deep analysis of the request."""
        try:
            # Create AI prompt for analysis
            prompt = self._create_analysis_prompt(request, pattern_analysis)
            
            ai_request = AIRequest(
                prompt=prompt,
                task_type=TaskType.ANALYSIS,
                context={
                    "system_prompt": "You are an expert software architect and project planner. Analyze the user's coding request and provide detailed insights."
                }
            )
            
            response = await self.ai_provider_manager.generate_response(ai_request)
            
            # Parse AI response
            return self._parse_ai_analysis(response.content)
            
        except Exception as e:
            print(f"AI analysis failed: {e}")
            # Fallback so flow can continue with useful defaults
            return {
                "follow_up_questions": self._generate_default_questions(
                    pattern_analysis.get("components", []),
                    pattern_analysis.get("technology_stack", {})
                ),
                "risk_factors": ["AI analysis unavailable or rate-limited"],
                "estimated_time": "2-4 weeks",
                "requirements": [],
                "constraints": []
            }
    
    def _create_analysis_prompt(self, request: str, pattern_analysis: Dict[str, Any]) -> str:
        """Create a comprehensive prompt for AI analysis."""
        prompt = f"""
        Analyze this coding request and provide detailed insights:

        REQUEST: {request}

        INITIAL ANALYSIS:
        - Identified components: {[c.name for c in pattern_analysis.get('components', [])]}
        - Technology stack: {pattern_analysis.get('technology_stack', {})}
        - Project type: {pattern_analysis.get('project_type', 'unknown')}
        - Complexity: {pattern_analysis.get('complexity', 'unknown')}

        Please provide:
        1. Missing technology components that should be considered
        2. Risk factors and potential challenges
        3. Critical follow-up questions to clarify requirements
        4. Refined complexity assessment
        5. Time estimation for development
        6. Additional requirements that might be needed
        7. Constraints and limitations to consider

        Format your response as structured analysis.
        """
        return prompt
    
    def _parse_ai_analysis(self, ai_response: str) -> Dict[str, Any]:
        """Parse the AI analysis response."""
        # This is a simplified parser - in production, you'd want more sophisticated parsing
        analysis = {
            "follow_up_questions": [],
            "risk_factors": [],
            "estimated_time": "unknown",
            "requirements": [],
            "constraints": []
        }
        
        # Extract follow-up questions
        if "follow-up" in ai_response.lower() or "questions" in ai_response.lower():
            lines = ai_response.split('\n')
            for line in lines:
                if line.strip().startswith(('?', '-', '*', '1.', '2.', '3.')):
                    question = line.strip().lstrip('?*-123456789. ')
                    if question and len(question) > 10:
                        analysis["follow_up_questions"].append(question)
        
        # Extract risk factors
        if "risk" in ai_response.lower():
            lines = ai_response.split('\n')
            for line in lines:
                if any(word in line.lower() for word in ["risk", "challenge", "difficulty", "complex"]):
                    risk = line.strip().lstrip('*-123456789. ')
                    if risk and len(risk) > 10:
                        analysis["risk_factors"].append(risk)
        
        # Extract time estimation
        time_patterns = [
            r"(\d+)\s*(?:hours?|hrs?)",
            r"(\d+)\s*(?:days?)",
            r"(\d+)\s*(?:weeks?)",
            r"(\d+)\s*(?:months?)"
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, ai_response.lower())
            if match:
                analysis["estimated_time"] = match.group(0)
                break
        
        return analysis
    
    def _combine_analyses(self, request: str, pattern_analysis: Dict[str, Any], ai_analysis: Dict[str, Any]) -> RequestAnalysis:
        """Combine pattern and AI analysis into final result."""
        components = pattern_analysis.get("components", [])
        technology_stack = pattern_analysis.get("technology_stack", {})
        project_type = pattern_analysis.get("project_type", "web_application")
        complexity = pattern_analysis.get("complexity", "medium")
        
        # Merge AI insights
        follow_up_questions = ai_analysis.get("follow_up_questions", [])
        risk_factors = ai_analysis.get("risk_factors", [])
        estimated_time = ai_analysis.get("estimated_time", "2-4 weeks")
        requirements = ai_analysis.get("requirements", [])
        constraints = ai_analysis.get("constraints", [])
        
        # Add default follow-up questions if AI didn't provide any
        if not follow_up_questions:
            follow_up_questions = self._generate_default_questions(components, technology_stack)
        
        return RequestAnalysis(
            original_request=request,
            identified_components=components,
            complexity_level=complexity,
            estimated_time=estimated_time,
            risk_factors=risk_factors,
            follow_up_questions=follow_up_questions,
            technology_stack=technology_stack,
            project_type=project_type,
            requirements=requirements,
            constraints=constraints
        )
    
    def _calculate_confidence(self, request: str, pattern: str, tech_name: str) -> float:
        """Calculate confidence level for a technology identification."""
        base_confidence = 0.5
        
        # Higher confidence for exact matches
        if tech_name in request:
            base_confidence += 0.3
        
        # Higher confidence for specific mentions
        if pattern in request:
            base_confidence += 0.2
        
        # Context-based adjustments
        if any(word in request for word in ["build", "create", "develop", "make"]):
            base_confidence += 0.1
        
        return min(base_confidence, 1.0)
    
    def _get_alternatives(self, category: str, tech_name: str) -> List[str]:
        """Get alternative technologies for a category."""
        alternatives = []
        if category in self.tech_patterns:
            alternatives = list(self.tech_patterns[category].keys())
            if tech_name in alternatives:
                alternatives.remove(tech_name)
        return alternatives[:3]  # Limit to top 3 alternatives
    
    def _get_tech_description(self, tech_name: str) -> str:
        """Get description for a technology."""
        descriptions = {
            "react": "Modern JavaScript library for building user interfaces",
            "vue": "Progressive JavaScript framework for building UIs",
            "angular": "Platform for building mobile and desktop web applications",
            "python": "High-level programming language with extensive libraries",
            "fastapi": "Modern, fast web framework for building APIs with Python",
            "postgresql": "Advanced open-source relational database",
            "mongodb": "Document-oriented NoSQL database",
            "docker": "Platform for developing, shipping, and running applications"
        }
        return descriptions.get(tech_name, f"{tech_name} technology")
    
    def _identify_project_type(self, request: str) -> str:
        """Identify the type of project from the request."""
        request_lower = request.lower()
        
        if any(word in request_lower for word in ["fullstack", "full-stack", "full stack"]):
            return "fullstack_application"
        if any(word in request_lower for word in ["api", "backend", "server"]):
            return "backend_api"
        elif any(word in request_lower for word in ["frontend", "ui", "interface", "web app", "spa", "website"]):
            return "frontend_application"
        elif any(word in request_lower for word in ["mobile", "ios", "android", "react native", "flutter"]):
            return "mobile_application"
        elif any(word in request_lower for word in ["desktop", "gui", "application"]):
            return "desktop_application"
        else:
            return "web_application"
    
    def _estimate_complexity(self, request: str, component_count: int) -> str:
        """Estimate project complexity."""
        complexity_score = 0
        
        # Component count factor
        complexity_score += component_count * 0.3
        
        # Request length factor
        complexity_score += len(request.split()) * 0.01
        
        # Feature complexity indicators
        complex_features = ["authentication", "database", "api", "deployment", "testing", "security"]
        for feature in complex_features:
            if feature in request.lower():
                complexity_score += 0.2
        
        if complexity_score < 1.0:
            return "simple"
        elif complexity_score < 2.5:
            return "medium"
        elif complexity_score < 4.0:
            return "complex"
        else:
            return "enterprise"
    
    def _generate_default_questions(self, components: List[TechnologyComponent], tech_stack: Dict[str, str]) -> List[str]:
        """Generate default follow-up questions based on identified components."""
        questions = []
        
        # Frontend questions
        if "frontend" not in tech_stack:
            questions.append("What type of user interface do you need? (web app, mobile app, desktop app)")
        
        # Backend questions
        if "backend" not in tech_stack:
            questions.append("Do you need a backend API or server-side logic?")
        
        # Database questions
        if "database" not in tech_stack:
            questions.append("Do you need to store data? What type of data will you be working with?")
        
        # Deployment questions
        if "deployment" not in tech_stack:
            questions.append("Where do you plan to deploy this application? (cloud, local, specific platform)")
        
        # Authentication questions
        if any(word in str(components).lower() for word in ["user", "login", "account"]):
            questions.append("Do you need user authentication and user management?")
        
        return questions[:5]  # Limit to 5 questions
