"""
AI Provider Manager for intelligent provider selection and routing.
"""

import asyncio
import time
from typing import Dict, Any, List, Optional
from enum import Enum
from .base_provider import (
    AIProvider, AIRequest, AIResponse, TaskType, RateLimitError,
    QuotaExceededError, AuthenticationError, ProviderTimeoutError,
    ProviderUnavailableError
)
from .openai_client import OpenAIClient
from .gemini_client import GeminiClient
from .claude_client import Claude<PERSON><PERSON>
from ..config import config_manager


class CircuitState(Enum):
    """Circuit breaker states for provider health management."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Provider is failing, requests blocked
    HALF_OPEN = "half_open"  # Testing if provider has recovered

class AIProviderManager:
    """Manages multiple AI providers with intelligent routing and circuit breaker pattern."""

    def __init__(self):
        self.config = config_manager.get_config()
        self.providers: Dict[str, AIProvider] = {}
        self.provider_stats: Dict[str, Dict[str, Any]] = {}

        # Rate limiting
        self.provider_last_call_time: Dict[str, float] = {}
        self.provider_min_interval: Dict[str, float] = {}
        self.provider_rate_limited_until: Dict[str, float] = {}

        # Circuit breaker pattern for provider health
        self.circuit_states: Dict[str, CircuitState] = {}
        self.failure_counts: Dict[str, int] = {}
        self.last_failure_time: Dict[str, float] = {}
        self.circuit_open_time: Dict[str, float] = {}

        # Configuration for circuit breaker
        self.failure_threshold = 5  # Open circuit after 5 consecutive failures
        self.recovery_timeout = 60  # Try to recover after 60 seconds
        self.half_open_max_calls = 3  # Allow 3 test calls in half-open state

        self.verbose: bool = False
        self._initialize_providers()
    
    def _initialize_providers(self) -> None:
        """Initialize all configured AI providers."""
        # Initialize OpenAI
        if self.config.ai_providers.openai_api_key:
            try:
                self.providers["openai"] = OpenAIClient(
                    api_key=self.config.ai_providers.openai_api_key,
                    config={"max_retries": self.config.ai_providers.max_retries}
                )
            except Exception as e:
                print(f"Failed to initialize OpenAI: {e}")
        
        # Initialize Gemini
        if self.config.ai_providers.google_ai_api_key:
            try:
                self.providers["gemini"] = GeminiClient(
                    api_key=self.config.ai_providers.google_ai_api_key,
                    config={"max_retries": self.config.ai_providers.max_retries}
                )
            except Exception as e:
                print(f"Failed to initialize Gemini: {e}")
        
        # Initialize Claude
        if self.config.ai_providers.anthropic_api_key:
            try:
                self.providers["claude"] = ClaudeClient(
                    api_key=self.config.ai_providers.anthropic_api_key,
                    config={"max_retries": self.config.ai_providers.max_retries}
                )
            except Exception as e:
                print(f"Failed to initialize Claude: {e}")
        
        # Initialize provider statistics and circuit breaker state
        for provider_name in self.providers:
            self.provider_stats[provider_name] = {
                "total_requests": 0,
                "successful_requests": 0,
                "failed_requests": 0,
                "consecutive_failures": 0,
                "total_cost": 0.0,
                "average_response_time": 0.0,
                "last_used": None,
                "circuit_state": CircuitState.CLOSED.value
            }
            self.provider_last_call_time[provider_name] = 0.0
            self.provider_min_interval[provider_name] = 0.0
            self.circuit_states[provider_name] = CircuitState.CLOSED
            self.failure_counts[provider_name] = 0
            self.last_failure_time[provider_name] = 0.0
            self.circuit_open_time[provider_name] = 0.0
    
    async def test_all_providers(self) -> Dict[str, bool]:
        """Test all available providers."""
        results = {}
        for name, provider in self.providers.items():
            try:
                results[name] = await provider.test_connection()
                print(f"✅ {name}: {'Available' if results[name] else 'Unavailable'}")
            except Exception as e:
                results[name] = False
                print(f"❌ {name}: Error - {e}")
        
        return results
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers."""
        return [name for name, provider in self.providers.items() if provider.is_available]
    
    def select_provider(self, task_type: TaskType, preferred_provider: Optional[str] = None) -> Optional[AIProvider]:
        """Select the best provider for a given task."""
        # Honor explicitly provided preference; fall back to configured preference
        if not preferred_provider or preferred_provider == "auto":
            preferred_provider = self.config.ai_providers.preferred_provider

        if preferred_provider and preferred_provider != "auto" and preferred_provider in self.providers:
            provider = self.providers[preferred_provider]
            if provider.is_available:
                if self.verbose:
                    print(f"🔀 Provider selection: prefer='{preferred_provider}' → using {provider.name}")
                return provider
        
        # Smart provider selection based on task type and circuit breaker state
        suitable_providers = []
        now = asyncio.get_event_loop().time()
        for name, provider in self.providers.items():
            if not provider.is_available:
                continue

            # Check circuit breaker state
            if not self._is_provider_available_for_request(name, now):
                continue

            # Check rate limiting
            until = self.provider_rate_limited_until.get(name, 0.0)
            if until and now < until:
                if self.verbose:
                    remaining = until - now
                    print(f"🚫 Skipping {name} (rate-limited ~{remaining:.0f}s left)")
                continue

            if provider.is_suitable_for_task(task_type):
                suitable_providers.append((name, provider))
        
        if not suitable_providers:
            # Fallback to any available provider
            suitable_providers = [(name, provider) for name, provider in self.providers.items() if provider.is_available]
        
        if not suitable_providers:
            return None
        
        # Select provider based on performance and cost
        best_provider = None
        best_score = float('-inf')
        
        for name, provider in suitable_providers:
            stats = self.provider_stats.get(name, {})
            
            # Calculate provider score
            success_rate = stats.get("successful_requests", 0) / max(stats.get("total_requests", 1), 1)
            avg_cost = stats.get("total_cost", 0) / max(stats.get("total_requests", 1), 1)
            
            # Score based on success rate and cost (lower cost is better)
            score = success_rate * 100 - avg_cost * 1000
            
            if score > best_score:
                best_score = score
                best_provider = provider
        if self.verbose and best_provider:
            print(f"🤖 Provider selection: task={task_type.value} candidates={[n for n,_ in suitable_providers]} → chosen={best_provider.name}")
        
        return best_provider
    
    async def generate_response(self, request: AIRequest, preferred_provider: Optional[str] = None) -> AIResponse:
        """Generate response using the best available provider."""
        provider = self.select_provider(request.task_type, preferred_provider)
        
        if not provider:
            raise Exception("No available AI providers")
        
        provider_name = provider.name.lower().replace("client", "")
        
        try:
            # Adaptive rate limiting: ensure min interval between calls for this provider
            now = asyncio.get_event_loop().time()
            min_interval = self.provider_min_interval.get(provider_name, 0.0)
            last_time = self.provider_last_call_time.get(provider_name, 0.0)
            wait_for = min_interval - (now - last_time)
            if wait_for > 0:
                if self.verbose:
                    print(f"⏱️ Waiting {wait_for:.1f}s before calling {provider_name}")
                await asyncio.sleep(wait_for)

            # Update statistics
            self.provider_stats[provider_name]["total_requests"] += 1
            self.provider_stats[provider_name]["last_used"] = asyncio.get_event_loop().time()
            
            # Generate response
            start_time = asyncio.get_event_loop().time()
            try:
                response = await provider.generate_response(request)
                # Success - reset circuit breaker
                self._record_success(provider_name)
            except Exception as err:
                # Record failure for circuit breaker
                self._record_failure(provider_name, now)

                # Handle specific error types
                if isinstance(err, RateLimitError):
                    # Backoff for suggested duration (capped)
                    cfg = config_manager.get_config()
                    cap = float(getattr(cfg, 'max_rate_limit_wait_seconds', 60))
                    backoff = min(cap, max(5.0, getattr(err, 'retry_after_seconds', 15.0)))
                    self.provider_min_interval[provider_name] = max(self.provider_min_interval.get(provider_name, 0.0), backoff)
                    now_ts = asyncio.get_event_loop().time()
                    self.provider_last_call_time[provider_name] = now_ts
                    self.provider_rate_limited_until[provider_name] = now_ts + backoff
                    if self.verbose:
                        print(f"⏳ Rate limit on {provider_name}, backing off for ~{backoff:.0f}s")

                    # Try fallback provider
                    fallback = self._select_fallback_provider(task_type=request.task_type, exclude=provider_name)
                    if fallback:
                        if self.verbose:
                            print(f"🔁 Fallback provider: {fallback.name}")
                        response = await fallback.generate_response(request)
                        provider_name = fallback.name.lower().replace("client", "")
                        self._record_success(provider_name)
                    else:
                        raise

                elif isinstance(err, (QuotaExceededError, AuthenticationError)):
                    # These are serious errors - open circuit immediately
                    self._open_circuit(provider_name, now)
                    fallback = self._select_fallback_provider(task_type=request.task_type, exclude=provider_name)
                    if fallback:
                        if self.verbose:
                            print(f"🔁 Provider {provider_name} failed ({type(err).__name__}), using fallback: {fallback.name}")
                        response = await fallback.generate_response(request)
                        provider_name = fallback.name.lower().replace("client", "")
                        self._record_success(provider_name)
                    else:
                        raise

                elif isinstance(err, (ProviderTimeoutError, ProviderUnavailableError)):
                    # Temporary issues - try fallback
                    fallback = self._select_fallback_provider(task_type=request.task_type, exclude=provider_name)
                    if fallback:
                        if self.verbose:
                            print(f"🔁 Provider {provider_name} unavailable, using fallback: {fallback.name}")
                        response = await fallback.generate_response(request)
                        provider_name = fallback.name.lower().replace("client", "")
                        self._record_success(provider_name)
                    else:
                        raise
                else:
                    # Unknown error - try fallback once
                    fallback = self._select_fallback_provider(task_type=request.task_type, exclude=provider_name)
                    if fallback:
                        if self.verbose:
                            print(f"🔁 Provider {provider_name} error ({type(err).__name__}), trying fallback: {fallback.name}")
                        try:
                            response = await fallback.generate_response(request)
                            provider_name = fallback.name.lower().replace("client", "")
                            self._record_success(provider_name)
                        except Exception:
                            # Fallback also failed, raise original error
                            raise err
                    else:
                        raise
            end_time = asyncio.get_event_loop().time()
            
            # Update success statistics
            self.provider_stats[provider_name]["successful_requests"] += 1
            self.provider_stats[provider_name]["total_cost"] += response.cost or 0.0
            
            # Update average response time
            current_avg = self.provider_stats[provider_name]["average_response_time"]
            total_requests = self.provider_stats[provider_name]["successful_requests"]
            new_avg = ((current_avg * (total_requests - 1)) + (end_time - start_time)) / total_requests
            self.provider_stats[provider_name]["average_response_time"] = new_avg
            # Update rate limiting dynamics
            # Target min interval ~ half of observed avg response time, bounded
            target_interval = max(0.0, min(2.0, new_avg * 0.5))
            # Move towards target smoothly
            current_interval = self.provider_min_interval.get(provider_name, 0.0)
            self.provider_min_interval[provider_name] = (current_interval * 0.7) + (target_interval * 0.3)
            self.provider_last_call_time[provider_name] = end_time
            
            return response
            
        except Exception as e:
            # Update failure statistics
            self.provider_stats[provider_name]["failed_requests"] += 1
            self._record_failure(provider_name, asyncio.get_event_loop().time())

            # Backoff on failure
            current_interval = self.provider_min_interval.get(provider_name, 0.0)
            self.provider_min_interval[provider_name] = min(5.0, current_interval + 0.25)
            self.provider_last_call_time[provider_name] = asyncio.get_event_loop().time()
            raise e

    def _select_fallback_provider(self, task_type: TaskType, exclude: str) -> Optional[AIProvider]:
        """Select a fallback provider different from the excluded one."""
        candidates = [
            p for name, p in self.providers.items()
            if name != exclude and p.is_available and p.is_suitable_for_task(task_type)
        ]
        if candidates:
            # naive best pick: first available
            return candidates[0]
        return None

    def set_verbose(self, enabled: bool) -> None:
        """Enable/disable verbose provider routing logs."""
        self.verbose = enabled
    
    def get_provider_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all providers."""
        return self.provider_stats.copy()
    
    def get_provider_health(self) -> Dict[str, Dict[str, Any]]:
        """Get health status for all providers including circuit breaker state."""
        health = {}
        current_time = asyncio.get_event_loop().time()

        for name, provider in self.providers.items():
            health[name] = provider.get_health_status()
            stats = self.provider_stats.get(name, {})
            health[name]["stats"] = stats

            # Add circuit breaker information
            circuit_state = self.circuit_states.get(name, CircuitState.CLOSED)
            health[name]["circuit_breaker"] = {
                "state": circuit_state.value,
                "consecutive_failures": self.failure_counts.get(name, 0),
                "last_failure_time": self.last_failure_time.get(name, 0),
                "available_for_requests": self._is_provider_available_for_request(name, current_time)
            }

            # Add rate limiting information
            health[name]["rate_limiting"] = {
                "min_interval": self.provider_min_interval.get(name, 0.0),
                "last_call_time": self.provider_last_call_time.get(name, 0.0),
                "rate_limited_until": self.provider_rate_limited_until.get(name, 0.0)
            }

        return health
    
    def estimate_cost(self, request: AIRequest, provider_name: Optional[str] = None) -> float:
        """Estimate cost for a request."""
        if provider_name and provider_name in self.providers:
            return self.providers[provider_name].estimate_cost(request)
        
        # Estimate with best available provider
        provider = self.select_provider(request.task_type)
        if provider:
            return provider.estimate_cost(request)
        
        return 0.0
    
    def get_recommended_provider(self, task_type: TaskType) -> str:
        """Get recommended provider for a task type."""
        provider = self.select_provider(task_type)
        if provider:
            for name, p in self.providers.items():
                if p == provider:
                    return name
        return "none"
    
    def _is_provider_available_for_request(self, provider_name: str, current_time: float) -> bool:
        """Check if provider is available based on circuit breaker state."""
        circuit_state = self.circuit_states.get(provider_name, CircuitState.CLOSED)

        if circuit_state == CircuitState.CLOSED:
            return True
        elif circuit_state == CircuitState.OPEN:
            # Check if recovery timeout has passed
            open_time = self.circuit_open_time.get(provider_name, 0)
            if current_time - open_time >= self.recovery_timeout:
                # Move to half-open state
                self.circuit_states[provider_name] = CircuitState.HALF_OPEN
                self.provider_stats[provider_name]["circuit_state"] = CircuitState.HALF_OPEN.value
                if self.verbose:
                    print(f"🔄 Circuit breaker for {provider_name} moved to HALF_OPEN")
                return True
            return False
        elif circuit_state == CircuitState.HALF_OPEN:
            # Allow limited requests in half-open state
            return True

        return False

    def _record_success(self, provider_name: str) -> None:
        """Record a successful request and update circuit breaker state."""
        self.failure_counts[provider_name] = 0
        self.provider_stats[provider_name]["consecutive_failures"] = 0

        # If circuit was half-open, close it
        if self.circuit_states.get(provider_name) == CircuitState.HALF_OPEN:
            self.circuit_states[provider_name] = CircuitState.CLOSED
            self.provider_stats[provider_name]["circuit_state"] = CircuitState.CLOSED.value
            if self.verbose:
                print(f"✅ Circuit breaker for {provider_name} CLOSED (recovered)")

    def _record_failure(self, provider_name: str, current_time: float) -> None:
        """Record a failed request and update circuit breaker state."""
        self.failure_counts[provider_name] = self.failure_counts.get(provider_name, 0) + 1
        self.provider_stats[provider_name]["consecutive_failures"] = self.failure_counts[provider_name]
        self.last_failure_time[provider_name] = current_time

        # Check if we should open the circuit
        if self.failure_counts[provider_name] >= self.failure_threshold:
            self._open_circuit(provider_name, current_time)

    def _open_circuit(self, provider_name: str, current_time: float) -> None:
        """Open the circuit breaker for a provider."""
        self.circuit_states[provider_name] = CircuitState.OPEN
        self.circuit_open_time[provider_name] = current_time
        self.provider_stats[provider_name]["circuit_state"] = CircuitState.OPEN.value
        if self.verbose:
            print(f"🚫 Circuit breaker for {provider_name} OPENED (too many failures)")

    def __str__(self) -> str:
        available = self.get_available_providers()
        return f"AIProviderManager(providers={list(self.providers.keys())}, available={available})"
