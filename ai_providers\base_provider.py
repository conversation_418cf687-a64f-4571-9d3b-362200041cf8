"""
Abstract Base Class for AI Providers
Defines the interface that all AI providers must implement
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

class TaskType(Enum):
    """Types of tasks that can be performed"""
    ANALYSIS = "analysis"
    CODE_GENERATION = "code_generation"
    OPTIMIZATION = "optimization"
    REVIEW = "review"
    PLANNING = "planning"
    DEBUGGING = "debugging"

@dataclass
class AIRequest:
    """Standardized request format for AI providers"""
    prompt: str
    task_type: TaskType
    context: Optional[Dict[str, Any]] = None
    max_tokens: Optional[int] = None
    temperature: float = 0.7
    system_message: Optional[str] = None

@dataclass
class AIResponse:
    """Standardized response format from AI providers"""
    content: str
    provider: str
    model: str
    tokens_used: Optional[int] = None
    cost: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

class AIProvider(ABC):
    """Abstract base class for AI providers"""
    
    def __init__(self, api_key: str, config: Dict[str, Any]):
        self.api_key = api_key
        self.config = config
        self.name = self.__class__.__name__
        self.is_available = False
        self._initialize()
    
    @abstractmethod
    def _initialize(self):
        """Initialize the provider with API key and configuration"""
        pass
    
    @abstractmethod
    async def generate_response(self, request: AIRequest) -> AIResponse:
        """Generate a response for the given request"""
        pass
    
    @abstractmethod
    def get_models(self) -> List[str]:
        """Get available models for this provider"""
        pass
    
    @abstractmethod
    def estimate_cost(self, request: AIRequest) -> float:
        """Estimate the cost for the given request"""
        pass
    
    def is_suitable_for_task(self, task_type: TaskType) -> bool:
        """Check if this provider is suitable for the given task type"""
        # Default implementation - can be overridden by subclasses
        return True
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about this provider"""
        return {
            "name": self.name,
            "is_available": self.is_available,
            "models": self.get_models(),
            "supported_tasks": [task.value for task in TaskType]
        }
    
    async def health_check(self) -> bool:
        """Check if the provider is healthy and available"""
        try:
            # Simple test request
            test_request = AIRequest(
                prompt="Hello",
                task_type=TaskType.ANALYSIS,
                max_tokens=10
            )
            await self.generate_response(test_request)
            self.is_available = True
            return True
        except Exception:
            self.is_available = False
            return False
