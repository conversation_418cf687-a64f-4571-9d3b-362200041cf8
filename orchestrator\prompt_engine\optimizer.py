"""
Prompt Optimizer for generating perfect prompts for <PERSON>urs<PERSON>.
"""

import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from ..ai_providers.base_provider import TaskType, AIRequest
from ..ai_providers.provider_manager import AIProviderManager
from .templates import PromptTemplates
from .learning import LearningEngine

@dataclass
class OptimizedPrompt:
    """An optimized prompt for Cursor."""
    original_request: str
    optimized_prompt: str
    context: Dict[str, Any]
    task_type: str
    confidence_score: float
    provider: str
    metadata: Dict[str, Any]

class PromptOptimizer:
    """Optimizes prompts for maximum effectiveness with Cursor."""
    
    def __init__(self, ai_provider_manager: AIProviderManager, learning_engine: LearningEngine):
        self.ai_provider_manager = ai_provider_manager
        self.learning_engine = learning_engine
        self.templates = PromptTemplates()
        
        # Prompt optimization strategies
        self.optimization_strategies = {
            "code_generation": self._optimize_code_generation,
            "refactoring": self._optimize_refactoring,
            "debugging": self._optimize_debugging,
            "testing": self._optimize_testing,
            "documentation": self._optimize_documentation,
            "architecture": self._optimize_architecture
        }
    
    async def optimize_prompt(self, user_request: str, context: Dict[str, Any] = None) -> OptimizedPrompt:
        """Generate an optimized prompt for the user request."""
        print(f"🔧 Optimizing prompt: {user_request[:100]}...")
        
        # Analyze the request type
        task_type = self._classify_request(user_request)
        
        # Get relevant context
        enhanced_context = self._enhance_context(context or {}, user_request)
        
        # Apply optimization strategy
        if task_type in self.optimization_strategies:
            optimized_prompt = await self.optimization_strategies[task_type](user_request, enhanced_context)
        else:
            optimized_prompt = await self._optimize_general(user_request, enhanced_context)
        
        # Learn from this optimization
        await self.learning_engine.record_prompt_attempt(user_request, optimized_prompt, task_type)
        
        # Get confidence score
        confidence_score = self._calculate_confidence(user_request, optimized_prompt, task_type)
        
        # Select best AI provider for this task based on classification
        provider_task = TaskType.CODE_GENERATION if task_type == "code_generation" else TaskType.OPTIMIZATION
        provider = self.ai_provider_manager.get_recommended_provider(provider_task)
        
        return OptimizedPrompt(
            original_request=user_request,
            optimized_prompt=optimized_prompt,
            context=enhanced_context,
            task_type=task_type,
            confidence_score=confidence_score,
            provider=provider,
            metadata={
                "optimization_strategy": task_type,
                "context_keys": list(enhanced_context.keys()),
                "prompt_length": len(optimized_prompt)
            }
        )
    
    def _classify_request(self, request: str) -> str:
        """Classify the type of request."""
        request_lower = request.lower()
        
        # Code generation patterns
        if any(word in request_lower for word in ["create", "build", "make", "generate"]):
            return "code_generation"
        
        # Refactoring patterns
        if any(word in request_lower for word in ["refactor", "improve", "optimize", "clean up", "restructure"]):
            return "refactoring"
        
        # Debugging patterns
        if any(word in request_lower for word in ["fix", "debug", "error", "bug", "issue", "problem"]):
            return "debugging"
        
        # Testing patterns
        if any(word in request_lower for word in ["test", "testing", "unit test", "integration test", "write tests", "tests"]):
            return "testing"
        
        # Documentation patterns
        if any(word in request_lower for word in ["document", "comment", "explain", "readme", "docs"]):
            return "documentation"
        
        # Architecture patterns
        if any(word in request_lower for word in ["architecture", "design", "structure", "pattern", "layout"]):
            return "architecture"
        
        return "general"
    
    def _enhance_context(self, context: Dict[str, Any], request: str) -> Dict[str, Any]:
        """Enhance context with additional relevant information."""
        enhanced = context.copy()
        
        # Add request analysis
        enhanced["request_analysis"] = {
            "length": len(request),
            "complexity": self._estimate_complexity(request),
            "keywords": self._extract_keywords(request)
        }
        
        # Add coding style preferences if available
        if "coding_style" not in enhanced:
            enhanced["coding_style"] = {
                "language": "python",  # Default
                "framework": "standard",
                "conventions": "pep8"
            }
        
        # Add project context if available
        if "project_context" not in enhanced:
            enhanced["project_context"] = {
                "type": "general",
                "scale": "medium"
            }
        
        return enhanced
    
    async def _optimize_code_generation(self, request: str, context: Dict[str, Any]) -> str:
        """Optimize prompt for code generation tasks."""
        template = self.templates.get_template("code_generation")
        
        # Enhance with specific details
        enhanced_request = self._add_code_generation_context(request, context)
        
        # Use AI to further optimize
        ai_optimized = await self._ai_optimize_prompt(enhanced_request, "code_generation", context)
        
        return ai_optimized or enhanced_request
    
    async def _optimize_refactoring(self, request: str, context: Dict[str, Any]) -> str:
        """Optimize prompt for refactoring tasks."""
        template = self.templates.get_template("refactoring")
        
        # Add refactoring-specific context
        enhanced_request = self._add_refactoring_context(request, context)
        
        # Use AI to optimize
        ai_optimized = await self._ai_optimize_prompt(enhanced_request, "refactoring", context)
        
        return ai_optimized or enhanced_request
    
    async def _optimize_debugging(self, request: str, context: Dict[str, Any]) -> str:
        """Optimize prompt for debugging tasks."""
        template = self.templates.get_template("debugging")
        
        # Add debugging-specific context
        enhanced_request = self._add_debugging_context(request, context)
        
        # Use AI to optimize
        ai_optimized = await self._ai_optimize_prompt(enhanced_request, "debugging", context)
        
        return ai_optimized or enhanced_request
    
    async def _optimize_testing(self, request: str, context: Dict[str, Any]) -> str:
        """Optimize prompt for testing tasks."""
        template = self.templates.get_template("testing")
        
        # Add testing-specific context
        enhanced_request = self._add_testing_context(request, context)
        
        # Use AI to optimize
        ai_optimized = await self._ai_optimize_prompt(enhanced_request, "testing", context)
        
        return ai_optimized or enhanced_request
    
    async def _optimize_documentation(self, request: str, context: Dict[str, Any]) -> str:
        """Optimize prompt for documentation tasks."""
        template = self.templates.get_template("documentation")
        
        # Add documentation-specific context
        enhanced_request = self._add_documentation_context(request, context)
        
        # Use AI to optimize
        ai_optimized = await self._ai_optimize_prompt(enhanced_request, "documentation", context)
        
        return ai_optimized or enhanced_request
    
    async def _optimize_architecture(self, request: str, context: Dict[str, Any]) -> str:
        """Optimize prompt for architecture tasks."""
        template = self.templates.get_template("architecture")
        
        # Add architecture-specific context
        enhanced_request = self._add_architecture_context(request, context)
        
        # Use AI to optimize
        ai_optimized = await self._ai_optimize_prompt(enhanced_request, "architecture", context)
        
        return ai_optimized or enhanced_request
    
    async def _optimize_general(self, request: str, context: Dict[str, Any]) -> str:
        """Optimize prompt for general tasks."""
        # Use AI to optimize general prompts
        ai_optimized = await self._ai_optimize_prompt(request, "general", context)
        
        return ai_optimized or request
    
    async def _ai_optimize_prompt(self, request: str, task_type: str, context: Dict[str, Any]) -> Optional[str]:
        """Use AI to optimize the prompt."""
        try:
            prompt = f"""
            Optimize this prompt for maximum effectiveness with an AI coding assistant:
            
            ORIGINAL REQUEST: {request}
            TASK TYPE: {task_type}
            CONTEXT: {context}
            
            Please provide an optimized version that:
            1. Is clear and specific
            2. Includes necessary context
            3. Uses appropriate technical language
            4. Provides clear instructions
            5. Anticipates potential questions
            
            Return only the optimized prompt.
            """
            
            ai_request = AIRequest(
                prompt=prompt,
                task_type=TaskType.OPTIMIZATION,
                context={
                    "system_prompt": "You are an expert at optimizing prompts for AI coding assistants."
                }
            )
            
            response = await self.ai_provider_manager.generate_response(ai_request)
            return response.content.strip()
            
        except Exception as e:
            print(f"AI prompt optimization failed: {e}")
            # Basic local improvement fallback
            lines = [
                "Please provide a modern, accessible, and responsive implementation.",
                "Include clear structure, comments where non-obvious, and follow best practices.",
                "Anticipate missing context by proposing sensible defaults.",
            ]
            return (request.strip() + "\n\n" + "\n".join(lines)).strip()
    
    def _add_code_generation_context(self, request: str, context: Dict[str, Any]) -> str:
        """Add context specific to code generation."""
        enhanced = request
        
        # Add language context
        if "coding_style" in context and "language" in context["coding_style"]:
            language = context["coding_style"]["language"]
            enhanced += f"\n\nPlease write the code in {language}."
        
        # Add framework context
        if "coding_style" in context and "framework" in context["coding_style"]:
            framework = context["coding_style"]["framework"]
            if framework != "standard":
                enhanced += f"\nUse {framework} framework/conventions."
        
        # Add conventions context
        if "coding_style" in context and "conventions" in context["coding_style"]:
            conventions = context["coding_style"]["conventions"]
            enhanced += f"\nFollow {conventions} coding standards."
        
        return enhanced
    
    def _add_refactoring_context(self, request: str, context: Dict[str, Any]) -> str:
        """Add context specific to refactoring."""
        enhanced = request
        
        # Add refactoring goals
        enhanced += "\n\nPlease ensure the refactored code:"
        enhanced += "\n- Maintains the same functionality"
        enhanced += "\n- Improves readability and maintainability"
        enhanced += "\n- Follows best practices"
        enhanced += "\n- Includes appropriate comments"
        
        return enhanced
    
    def _add_debugging_context(self, request: str, context: Dict[str, Any]) -> str:
        """Add context specific to debugging."""
        enhanced = request
        
        # Add debugging approach
        enhanced += "\n\nPlease help me debug this by:"
        enhanced += "\n- Identifying the root cause"
        enhanced += "\n- Suggesting specific fixes"
        enhanced += "\n- Explaining why the issue occurred"
        enhanced += "\n- Providing prevention strategies"
        
        return enhanced
    
    def _add_testing_context(self, request: str, context: Dict[str, Any]) -> str:
        """Add context specific to testing."""
        enhanced = request
        
        # Add testing requirements
        enhanced += "\n\nPlease create tests that:"
        enhanced += "\n- Cover the main functionality"
        enhanced += "\n- Include edge cases"
        enhanced += "\n- Are easy to understand and maintain"
        enhanced += "\n- Follow testing best practices"
        
        return enhanced
    
    def _add_documentation_context(self, request: str, context: Dict[str, Any]) -> str:
        """Add context specific to documentation."""
        enhanced = request
        
        # Add documentation requirements
        enhanced += "\n\nPlease create documentation that:"
        enhanced += "\n- Is clear and comprehensive"
        enhanced += "\n- Includes examples where helpful"
        enhanced += "\n- Follows documentation standards"
        enhanced += "\n- Is easy for other developers to understand"
        
        return enhanced
    
    def _add_architecture_context(self, request: str, context: Dict[str, Any]) -> str:
        """Add context specific to architecture."""
        enhanced = request
        
        # Add architecture requirements
        enhanced += "\n\nPlease design an architecture that:"
        enhanced += "\n- Is scalable and maintainable"
        enhanced += "\n- Follows design patterns and principles"
        enhanced += "\n- Considers performance and security"
        enhanced += "\n- Is well-documented with rationale"
        
        return enhanced
    
    def _estimate_complexity(self, request: str) -> str:
        """Estimate the complexity of the request."""
        word_count = len(request.split())
        
        if word_count < 3:
            return "simple"
        elif word_count < 6:
            return "medium"
        elif word_count < 100:
            return "complex"
        else:
            return "very_complex"
    
    def _extract_keywords(self, request: str) -> List[str]:
        """Extract key technical keywords from the request."""
        # Common technical terms
        tech_keywords = [
            "api", "database", "frontend", "backend", "authentication", "deployment",
            "testing", "performance", "security", "scalability", "maintainability",
            "framework", "library", "package", "module", "component", "service",
            "model", "controller", "router", "middleware", "validation", "error handling"
        ]
        
        found_keywords = []
        request_lower = request.lower()
        
        for keyword in tech_keywords:
            if keyword in request_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _calculate_confidence(self, original: str, optimized: str, task_type: str) -> float:
        """Calculate confidence score for the optimization."""
        base_score = 0.5
        
        # Length improvement
        if len(optimized) > len(original):
            base_score += 0.1
        
        # Structure improvement
        if "\n" in optimized and "\n" not in original:
            base_score += 0.1
        
        # Context addition
        if any(word in optimized.lower() for word in ["please", "ensure", "include", "follow"]):
            base_score += 0.1
        
        # Technical specificity
        tech_terms = self._extract_keywords(optimized)
        if len(tech_terms) > 0:
            base_score += 0.1
        
        # Task-specific optimization
        if task_type in self.optimization_strategies:
            base_score += 0.1
        
        return min(base_score, 1.0)
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get statistics about prompt optimization."""
        return {
            "total_optimizations": self.learning_engine.get_total_attempts(),
            "success_rate": self.learning_engine.get_success_rate(),
            "average_confidence": self.learning_engine.get_average_confidence(),
            "task_type_distribution": self.learning_engine.get_task_type_distribution()
        }
