/**
 * ReactApp - Main Application Component
 * Generated by AI Coding Orchestrator
 * 
 * A production-ready React component with proper state management,
 * error boundaries, and modern React patterns.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import './App.css';

// Custom hooks
const useLocalStorage = (key, initialValue) => {
    const [storedValue, setStoredValue] = useState(() => {
        try {
            const item = window.localStorage.getItem(key);
            return item ? JSON.parse(item) : initialValue;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return initialValue;
        }
    });

    const setValue = (value) => {
        try {
            setStoredValue(value);
            window.localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Error writing to localStorage:', error);
        }
    };

    return [storedValue, setValue];
};

// Error Boundary Component
class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }

    componentDidCatch(error, errorInfo) {
        console.error('Error caught by boundary:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div className="error-boundary">
                    <h2>Something went wrong!</h2>
                    <p>Please refresh the page or contact support.</p>
                    <button onClick={() => window.location.reload()}>
                        Refresh Page
                    </button>
                </div>
            );
        }

        return this.props.children;
    }
}

// Main App Component
function ReactApp() {
    const [count, setCount] = useState(0);
    const [theme, setTheme] = useLocalStorage('theme', 'light');
    const [isLoading, setIsLoading] = useState(false);

    // Memoized values
    const isDarkTheme = useMemo(() => theme === 'dark', [theme]);
    const themeStyles = useMemo(() => ({
        backgroundColor: isDarkTheme ? '#1a1a1a' : '#ffffff',
        color: isDarkTheme ? '#ffffff' : '#000000'
    }), [isDarkTheme]);

    // Event handlers
    const handleIncrement = useCallback(() => {
        setCount(prev => prev + 1);
    }, []);

    const handleDecrement = useCallback(() => {
        setCount(prev => Math.max(0, prev - 1));
    }, []);

    const handleReset = useCallback(() => {
        setCount(0);
    }, []);

    const toggleTheme = useCallback(() => {
        setTheme(prev => prev === 'light' ? 'dark' : 'light');
    }, [setTheme]);

    const handleAsyncOperation = useCallback(async () => {
        setIsLoading(true);
        try {
            // Simulate async operation
            await new Promise(resolve => setTimeout(resolve, 1000));
            setCount(prev => prev + 10);
        } catch (error) {
            console.error('Async operation failed:', error);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // Effects
    useEffect(() => {
        document.title = `{count} - ReactApp`;
    }, [count]);

    useEffect(() => {
        document.body.style.backgroundColor = themeStyles.backgroundColor;
        document.body.style.color = themeStyles.color;
    }, [themeStyles]);

    // Render methods
    const renderCounter = () => (
        <div className="counter-section">
            <h2>Counter: {count}</h2>
            <div className="counter-controls">
                <button onClick={handleDecrement} disabled={count === 0}>
                    -
                </button>
                <button onClick={handleIncrement}>
                    +
                </button>
                <button onClick={handleReset} disabled={count === 0}>
                    Reset
                </button>
            </div>
        </div>
    );

    const renderThemeToggle = () => (
        <div className="theme-section">
            <button onClick={toggleTheme} className="theme-toggle">
                Switch to {isDarkTheme ? 'Light' : 'Dark'} Theme
            </button>
        </div>
    );

    const renderAsyncButton = () => (
        <div className="async-section">
            <button 
                onClick={handleAsyncOperation} 
                disabled={isLoading}
                className="async-button"
            >
                {isLoading ? 'Processing...' : 'Add 10 (Async)'}
            </button>
        </div>
    );

    return (
        <ErrorBoundary>
            <div className="App" style={themeStyles}>
                <header className="App-header">
                    <h1>🚀 {project_structure.project_name}</h1>
                    <p>Generated by AI Coding Orchestrator</p>
                </header>

                <main className="App-main">
                    {renderCounter()}
                    {renderThemeToggle()}
                    {renderAsyncButton()}
                </main>

                <footer className="App-footer">
                    <p>Built with React • Generated by AI</p>
                </footer>
            </div>
        </ErrorBoundary>
    );
}

export default ReactApp;
