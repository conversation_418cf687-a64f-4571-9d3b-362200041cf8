"""
Cursor integration for the AI Coding Orchestrator.
"""

import asyncio
import os
from typing import Dict, Any, List, Optional
from pathlib import Path
from ..prompt_engine.optimizer import OptimizedPrompt

class CursorIntegrator:
    """Handles integration with Cursor through file-based and clipboard methods."""
    
    def __init__(self, config):
        self.config = config
        self.instruction_file_path = Path(config.cursor.instruction_file_path)
        
        # Ensure the instruction file directory exists
        self.instruction_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    async def create_instruction_file(self, prompts: List[OptimizedPrompt], 
                                    project_plan: Dict[str, Any]) -> Optional[str]:
        """Create an instruction file that <PERSON><PERSON><PERSON> can read."""
        try:
            content = self._generate_instruction_content(prompts, project_plan)
            
            # Write to file
            with open(self.instruction_file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"📝 Created instruction file: {self.instruction_file_path}")
            return str(self.instruction_file_path)
            
        except Exception as e:
            print(f"Failed to create instruction file: {e}")
            return None
    
    def _generate_instruction_content(self, prompts: List[OptimizedPrompt], 
                                   project_plan: Dict[str, Any]) -> str:
        """Generate the content for the instruction file."""
        content = []
        
        # Header
        content.append("# AI Coding Orchestrator - Cursor Instructions")
        content.append("")
        content.append("This file contains optimized prompts and project information for Cursor.")
        content.append("")
        
        # Project Overview
        if "project_structure" in project_plan:
            structure = project_plan["project_structure"]
            content.append("## Project Overview")
            content.append(f"- **Project Name**: {structure.project_name}")
            content.append(f"- **Root Directory**: {structure.root_directory}")
            content.append(f"- **Project Location**: `{structure.root_directory}`")
            content.append(f"- **Full Path**: `{os.path.abspath(structure.root_directory)}`")
            content.append(f"- **Total Files**: {len(structure.files)}")
            content.append("")
        
        # Development Roadmap
        if "development_roadmap" in project_plan:
            roadmap = project_plan["development_roadmap"]
            content.append("## Development Roadmap")
            content.append(f"- **Total Estimated Time**: {roadmap.total_estimated_time}")
            content.append("")
            
            for i, phase in enumerate(roadmap.phases, 1):
                content.append(f"### Phase {i}: {phase.name}")
                content.append(f"**Description**: {phase.description}")
                content.append(f"**Estimated Time**: {phase.estimated_time}")
                content.append(f"**Priority**: {phase.priority}")
                content.append("")
                content.append("**Tasks**:")
                for task in phase.tasks:
                    content.append(f"- {task}")
                content.append("")
                content.append("**Deliverables**:")
                for deliverable in phase.deliverables:
                    content.append(f"- {deliverable}")
                content.append("")
        
        # Optimized Prompts
        content.append("## Optimized Prompts for Cursor")
        content.append("")
        
        for i, prompt in enumerate(prompts, 1):
            content.append(f"### Prompt {i}: {prompt.task_type}")
            content.append(f"**Confidence Score**: {prompt.confidence_score:.2f}")
            content.append(f"**Provider**: {prompt.provider}")
            content.append("")
            content.append("**Original Request**:")
            content.append(f"```")
            content.append(prompt.original_request)
            content.append("```")
            content.append("")
            content.append("**Optimized Prompt**:")
            content.append(f"```")
            content.append(prompt.optimized_prompt)
            content.append("```")
            content.append("")
        
        # Technology Recommendations
        if "technology_recommendations" in project_plan:
            recommendations = project_plan["technology_recommendations"]
            content.append("## Technology Recommendations")
            content.append("")
            
            for category, items in recommendations.items():
                if items:
                    content.append(f"### {category.title()}")
                    for item in items:
                        content.append(f"- {item}")
                    content.append("")
        
        # Usage Instructions
        content.append("## Usage Instructions")
        content.append("")
        content.append("1. **Copy the optimized prompts** above and paste them into Cursor")
        content.append("2. **Follow the development roadmap** for structured development")
        content.append("3. **Reference the project structure** for file organization")
        content.append("4. **Use technology recommendations** for additional tools and libraries")
        content.append("")
        content.append("---")
        content.append("*Generated by AI Coding Orchestrator*")
        
        return "\n".join(content)
    
    async def copy_to_clipboard(self, text: str) -> bool:
        """Copy text to clipboard if clipboard integration is enabled."""
        if not self.config.cursor.clipboard_integration:
            return False
        
        try:
            # Try to use pyperclip if available
            try:
                import pyperclip
                pyperclip.copy(text)
                print("📋 Copied optimized prompt to clipboard")
                return True
            except ImportError:
                # Fallback to platform-specific commands
                if os.name == 'nt':  # Windows
                    import subprocess
                    subprocess.run(['clip'], input=text.encode(), check=True)
                    print("📋 Copied optimized prompt to clipboard (Windows)")
                    return True
                elif os.name == 'posix':  # Linux/Mac
                    import subprocess
                    subprocess.run(['xclip', '-selection', 'clipboard'], input=text.encode(), check=True)
                    print("📋 Copied optimized prompt to clipboard (Linux/Mac)")
                    return True
                else:
                    print("⚠️ Clipboard integration not supported on this platform")
                    return False
                    
        except Exception as e:
            print(f"Failed to copy to clipboard: {e}")
            return False
    
    async def monitor_file_changes(self, callback: callable) -> None:
        """Monitor file changes in the project directory."""
        if not self.config.cursor.file_monitoring:
            return
        
        try:
            from watchdog.observers import Observer
            from watchdog.events import FileSystemEventHandler
            
            class ProjectFileHandler(FileSystemEventHandler):
                def __init__(self, callback):
                    self.callback = callback
                
                def on_modified(self, event):
                    if not event.is_directory:
                        asyncio.create_task(self.callback(event.src_path, "modified"))
                
                def on_created(self, event):
                    if not event.is_directory:
                        asyncio.create_task(self.callback(event.src_path, "created"))
                
                def on_deleted(self, event):
                    if not event.is_directory:
                        asyncio.create_task(self.callback(event.src_path, "deleted"))
            
            # Get project directory from config or current working directory
            project_dir = Path.cwd()
            
            event_handler = ProjectFileHandler(callback)
            observer = Observer()
            observer.schedule(event_handler, str(project_dir), recursive=True)
            observer.start()
            
            print(f"📁 Monitoring file changes in: {project_dir}")
            
            # Keep monitoring until stopped
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                observer.stop()
                observer.join()
                
        except ImportError:
            print("⚠️ File monitoring requires watchdog package. Install with: pip install watchdog")
        except Exception as e:
            print(f"File monitoring failed: {e}")
    
    async def validate_generated_code(self, file_path: str) -> Dict[str, Any]:
        """Validate generated code files."""
        if not self.config.cursor.auto_validation:
            return {"status": "validation_disabled"}
        
        try:
            validation_result = {
                "file_path": file_path,
                "status": "validated",
                "issues": [],
                "suggestions": []
            }
            
            # Basic file validation
            if not os.path.exists(file_path):
                validation_result["status"] = "file_not_found"
                return validation_result
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Basic content validation
            if not content.strip():
                validation_result["issues"].append("File is empty")
            
            # Language-specific validation
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext == '.py':
                validation_result.update(await self._validate_python_file(content))
            elif file_ext in ['.js', '.jsx', '.ts', '.tsx']:
                validation_result.update(await self._validate_javascript_file(content))
            elif file_ext == '.html':
                validation_result.update(await self._validate_html_file(content))
            elif file_ext == '.css':
                validation_result.update(await self._validate_css_file(content))
            
            return validation_result
            
        except Exception as e:
            return {
                "file_path": file_path,
                "status": "validation_error",
                "error": str(e)
            }
    
    async def _validate_python_file(self, content: str) -> Dict[str, Any]:
        """Validate Python file content."""
        validation = {"issues": [], "suggestions": []}
        
        # Check for basic Python syntax
        try:
            compile(content, '<string>', 'exec')
        except SyntaxError as e:
            validation["issues"].append(f"Syntax error: {e}")
        
        # Check for common Python issues
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if line.strip() and not line.startswith('#'):
                # Check for missing docstrings in functions/classes
                if line.strip().startswith(('def ', 'class ')):
                    if not any('"""' in l or "'''" in l for l in lines[i:i+3]):
                        validation["suggestions"].append(f"Line {i}: Consider adding docstring")
                
                # Check for long lines
                if len(line) > 79:
                    validation["suggestions"].append(f"Line {i}: Line too long ({len(line)} > 79 characters)")
        
        return validation
    
    async def _validate_javascript_file(self, content: str) -> Dict[str, Any]:
        """Validate JavaScript file content."""
        validation = {"issues": [], "suggestions": []}
        
        # Basic JavaScript validation
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if line.strip():
                # Check for missing semicolons
                if (line.strip().endswith((';', '{', '}', ':', ',')) or 
                    line.strip().startswith(('if', 'for', 'while', 'switch', 'try', 'catch', 'finally'))):
                    continue
                elif not line.strip().endswith(';'):
                    validation["suggestions"].append(f"Line {i}: Consider adding semicolon")
        
        return validation
    
    async def _validate_html_file(self, content: str) -> Dict[str, Any]:
        """Validate HTML file content."""
        validation = {"issues": [], "suggestions": []}
        
        # Basic HTML validation
        if '<!DOCTYPE html>' not in content and '<html' not in content:
            validation["suggestions"].append("Consider adding proper HTML structure")
        
        if '<title>' not in content:
            validation["suggestions"].append("Consider adding a title tag")
        
        return validation
    
    async def _validate_css_file(self, content: str) -> Dict[str, Any]:
        """Validate CSS file content."""
        validation = {"issues": [], "suggestions": []}
        
        # Basic CSS validation
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if line.strip() and ':' in line and not line.strip().endswith(';'):
                if not line.strip().endswith('}'):
                    validation["suggestions"].append(f"Line {i}: Consider adding semicolon")
        
        return validation
    
    def get_integration_status(self) -> Dict[str, Any]:
        """Get the current integration status."""
        return {
            "instruction_file_path": str(self.instruction_file_path),
            "instruction_file_exists": self.instruction_file_path.exists(),
            "clipboard_integration": self.config.cursor.clipboard_integration,
            "file_monitoring": self.config.cursor.file_monitoring,
            "auto_validation": self.config.cursor.auto_validation
        }
