"""
OpenAI client implementation for the AI Coding Orchestrator.
"""

import async<PERSON>
import <PERSON>ai
from typing import Dict, Any, List
from .base_provider import <PERSON>Provider, AIRequest, AIResponse, TaskType

class OpenAIClient(AIProvider):
    """OpenAI API client implementation."""
    
    def _initialize(self) -> None:
        """Initialize OpenAI client."""
        try:
            openai.api_key = self.api_key
            self.client = openai.AsyncOpenAI(api_key=self.api_key)
            self.is_available = True
        except Exception as e:
            print(f"Failed to initialize OpenAI client: {e}")
            self.is_available = False
    
    async def generate_response(self, request: AIRequest) -> AIResponse:
        """Generate response using OpenAI API."""
        if not self.is_available:
            raise Exception("OpenAI client is not available")
        
        try:
            # Select appropriate model based on task type
            model = self._select_model(request.task_type)
            
            # Prepare messages
            messages = self._prepare_messages(request)
            
            # Make API call
            response = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=request.max_tokens or 4000,
                temperature=request.temperature or 0.7
            )
            
            # Extract response content
            content = response.choices[0].message.content
            
            # Calculate cost (approximate)
            cost = self._calculate_cost(response.usage.total_tokens, model)
            
            return AIResponse(
                content=content,
                provider="OpenAI",
                model=model,
                tokens_used=response.usage.total_tokens,
                cost=cost,
                metadata={
                    "finish_reason": response.choices[0].finish_reason,
                    "response_time": getattr(response, 'response_time', None)
                }
            )
            
        except Exception as e:
            raise Exception(f"OpenAI API error: {e}")
    
    def _select_model(self, task_type: TaskType) -> str:
        """Select the best model for the task type."""
        # Prefer generally available models to reduce access errors on test
        model_mapping = {
            TaskType.ANALYSIS: "gpt-3.5-turbo",
            TaskType.CODE_GENERATION: "gpt-3.5-turbo",
            TaskType.OPTIMIZATION: "gpt-3.5-turbo",
            TaskType.REVIEW: "gpt-3.5-turbo",
            TaskType.PLANNING: "gpt-3.5-turbo"
        }
        return model_mapping.get(task_type, "gpt-3.5-turbo")
    
    def _prepare_messages(self, request: AIRequest) -> List[Dict[str, str]]:
        """Prepare messages for OpenAI API."""
        messages = []
        
        # Add system context if provided
        if request.context and "system_prompt" in request.context:
            messages.append({
                "role": "system",
                "content": request.context["system_prompt"]
            })
        
        # Add user prompt
        messages.append({
            "role": "user",
            "content": request.prompt
        })
        
        # Add additional context if provided
        if request.context and "additional_context" in request.context:
            messages.append({
                "role": "user",
                "content": f"Additional context: {request.context['additional_context']}"
            })
        
        return messages
    
    def _calculate_cost(self, tokens: int, model: str) -> float:
        """Calculate approximate cost for the request."""
        # OpenAI pricing (approximate, may vary)
        pricing = {
            "gpt-4": 0.03 / 1000,  # $0.03 per 1K tokens
            "gpt-4-turbo": 0.01 / 1000,  # $0.01 per 1K tokens
            "gpt-3.5-turbo": 0.002 / 1000,  # $0.002 per 1K tokens
        }
        
        base_cost = pricing.get(model, pricing["gpt-3.5-turbo"])
        return tokens * base_cost
    
    def get_models(self) -> List[str]:
        """Get available OpenAI models."""
        return [
            "gpt-4",
            "gpt-4-turbo", 
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k"
        ]
    
    def estimate_cost(self, request: AIRequest) -> float:
        """Estimate cost for a request."""
        # Rough estimation based on prompt length
        estimated_tokens = len(request.prompt.split()) * 1.3  # Rough token estimation
        model = self._select_model(request.task_type)
        return self._calculate_cost(int(estimated_tokens), model)
    
    def is_suitable_for_task(self, task_type: TaskType) -> bool:
        """Check if OpenAI is suitable for the task."""
        # OpenAI is excellent for all task types
        return True
