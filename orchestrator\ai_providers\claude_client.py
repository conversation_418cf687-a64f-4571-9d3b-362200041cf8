"""
Claude client implementation for the AI Coding Orchestrator.
"""

import asyncio
import anthropic
from typing import Dict, Any, List
from .base_provider import <PERSON>Provider, AIRequest, AIResponse, TaskType

class ClaudeClient(AIProvider):
    """Anthropic Claude API client implementation."""
    
    def _initialize(self) -> None:
        """Initialize Claude client."""
        try:
            self.client = anthropic.AsyncAnthropic(api_key=self.api_key)
            self.is_available = True
        except Exception as e:
            print(f"Failed to initialize Claude client: {e}")
            self.is_available = False
    
    async def generate_response(self, request: AIRequest) -> AIResponse:
        """Generate response using Claude API."""
        if not self.is_available:
            raise Exception("Claude client is not available")
        
        try:
            # Select appropriate model based on task type
            model = self._select_model(request.task_type)
            
            # Prepare messages
            messages = self._prepare_messages(request)
            
            # Make API call
            # Claude API expects one user message with text content
            content = "\n\n".join(m.get('content', '') for m in messages if m.get('role') == 'user')
            response = await self.client.messages.create(
                model=model,
                max_tokens=request.max_tokens or 4000,
                temperature=request.temperature or 0.7,
                messages=[{"role": "user", "content": content}]
            )
            
            # Extract response content
            content = response.content[0].text
            
            # Calculate cost (approximate)
            cost = self._calculate_cost(response.usage.input_tokens, response.usage.output_tokens, model)
            
            return AIResponse(
                content=content,
                provider="Claude",
                model=model,
                tokens_used=response.usage.input_tokens + response.usage.output_tokens,
                cost=cost,
                metadata={
                    "finish_reason": response.stop_reason,
                    "response_time": getattr(response, 'response_time', None)
                }
            )
            
        except Exception as e:
            raise Exception(f"Claude API error: {e}")
    
    def _select_model(self, task_type: TaskType) -> str:
        """Select the best model for the task type."""
        model_mapping = {
            TaskType.ANALYSIS: "claude-3-sonnet-20240229",
            TaskType.CODE_GENERATION: "claude-3-sonnet-20240229",
            TaskType.OPTIMIZATION: "claude-3-sonnet-20240229",
            TaskType.REVIEW: "claude-3-sonnet-20240229",
            TaskType.PLANNING: "claude-3-sonnet-20240229"
        }
        
        # Fallback to Haiku if Sonnet is not available
        try:
            return model_mapping.get(task_type, "claude-3-sonnet-20240229")
        except:
            return "claude-3-haiku-20240307"
    
    def _prepare_messages(self, request: AIRequest) -> List[Dict[str, str]]:
        """Prepare messages for Claude API."""
        messages = []
        
        # Add system prompt if provided
        if request.context and "system_prompt" in request.context:
            messages.append({
                "role": "user",
                "content": f"System: {request.context['system_prompt']}"
            })
        
        # Add main prompt
        messages.append({
            "role": "user",
            "content": request.prompt
        })
        
        # Add additional context if provided
        if request.context and "additional_context" in request.context:
            messages.append({
                "role": "user",
                "content": f"Additional context: {request.context['additional_context']}"
            })
        
        return messages
    
    def _calculate_cost(self, input_tokens: int, output_tokens: int, model: str) -> float:
        """Calculate approximate cost for the request."""
        # Claude pricing (approximate, may vary)
        pricing = {
            "claude-3-opus-20240229": {"input": 0.015, "output": 0.075},  # per 1K tokens
            "claude-3-sonnet-20240229": {"input": 0.003, "output": 0.015},  # per 1K tokens
            "claude-3-haiku-20240307": {"input": 0.00025, "output": 0.00125}  # per 1K tokens
        }
        
        model_pricing = pricing.get(model, pricing["claude-3-sonnet-20240229"])
        input_cost = (input_tokens / 1000) * model_pricing["input"]
        output_cost = (output_tokens / 1000) * model_pricing["output"]
        
        return input_cost + output_cost
    
    def get_models(self) -> List[str]:
        """Get available Claude models."""
        return [
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307"
        ]
    
    def estimate_cost(self, request: AIRequest) -> float:
        """Estimate cost for a request."""
        # Rough estimation based on prompt length
        estimated_input = len(request.prompt.split()) * 1.3
        estimated_output = 500  # Default output estimation
        model = self._select_model(request.task_type)
        return self._calculate_cost(int(estimated_input), estimated_output, model)
    
    def is_suitable_for_task(self, task_type: TaskType) -> bool:
        """Check if Claude is suitable for the task."""
        # Claude excels at code generation and optimization
        suitable_tasks = [
            TaskType.CODE_GENERATION,
            TaskType.OPTIMIZATION,
            TaskType.REVIEW
        ]
        return task_type in suitable_tasks
