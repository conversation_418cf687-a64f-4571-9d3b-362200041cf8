#!/usr/bin/env python3
"""
AI Coding Orchestrator - Main CLI Interface

A groundbreaking AI-powered system that sits above Cursor and manages entire development processes intelligently.
"""

import asyncio
import sys
import argparse
from pathlib import Path

# Add the orchestrator package to the path
sys.path.insert(0, str(Path(__file__).parent))

from orchestrator.orchestrator_main import OrchestratorMain
from orchestrator.config import config_manager

async def main():
    """Main entry point for the AI Coding Orchestrator."""
    parser = argparse.ArgumentParser(
        description="AI Coding Orchestrator - Intelligent development workflow management",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py "Build me a todo app with React frontend and Python FastAPI backend"
  python main.py --interactive
  python main.py --config
  python main.py --health
        """
    )
    
    parser.add_argument(
        "request",
        nargs="?",
        help="Your coding request (e.g., 'Build me a todo app')"
    )
    
    parser.add_argument(
        "--interactive", "-i",
        action="store_true",
        help="Run in interactive mode"
    )
    
    parser.add_argument(
        "--config", "-c",
        action="store_true",
        help="Create default configuration file"
    )
    
    parser.add_argument(
        "--health",
        action="store_true",
        help="Show system health status"
    )
    
    parser.add_argument(
        "--status", "-s",
        action="store_true",
        help="Show current session status"
    )
    
    parser.add_argument(
        "--history", "-y",
        action="store_true",
        help="Show session history"
    )
    
    parser.add_argument(
        "--export", "-e",
        metavar="SESSION_ID",
        help="Export session data for a specific session ID"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    args = parser.parse_args()
    
    # Handle configuration creation
    if args.config:
        config_manager.create_default_config()
        print("✅ Default configuration file created!")
        return
    
    # Initialize the orchestrator
    print("🚀 AI Coding Orchestrator")
    print("=" * 50)
    
    orchestrator = OrchestratorMain()
    
    # Initialize the system
    if not await orchestrator.initialize():
        print("❌ Failed to initialize orchestrator. Please check your configuration.")
        sys.exit(1)
    
    try:
        # Handle different modes
        if args.health:
            await show_system_health(orchestrator)
        elif args.status:
            await show_session_status(orchestrator)
        elif args.history:
            await show_session_history(orchestrator)
        elif args.export:
            await export_session_data(orchestrator, args.export)
        elif args.interactive:
            await orchestrator.run_interactive_mode()
        elif args.request:
            # Process a single request
            await process_single_request(orchestrator, args.request, args.verbose)
        else:
            # No arguments provided, show help
            parser.print_help()
            
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
    finally:
        await orchestrator.cleanup()

async def show_system_health(orchestrator: OrchestratorMain):
    """Display system health information."""
    print("\n🏥 System Health Report")
    print("=" * 50)
    
    health = orchestrator.get_system_health()
    
    print(f"Orchestrator Status: {health['orchestrator_status']}")
    print(f"AI Providers: {len(health['ai_providers'])} configured")
    
    # AI Provider details
    print("\n🤖 AI Provider Status:")
    for provider_name, provider_info in health['ai_providers'].items():
        status = "✅ Available" if provider_info.get('available', False) else "❌ Unavailable"
        print(f"  {provider_name}: {status}")
        if 'stats' in provider_info:
            stats = provider_info['stats']
            print(f"    Requests: {stats.get('total_requests', 0)}")
            print(f"    Success Rate: {stats.get('successful_requests', 0)/max(stats.get('total_requests', 1), 1)*100:.1f}%")
    
    # Learning system status
    print("\n🧠 Learning System Status:")
    learning_stats = health['learning_system']
    print(f"  Total Attempts: {learning_stats.get('total_attempts', 0)}")
    print(f"  Success Rate: {learning_stats.get('success_rate', 0)*100:.1f}%")
    print(f"  Average Confidence: {learning_stats.get('average_confidence', 0):.2f}")
    
    # Prompt optimization status
    print("\n🔧 Prompt Optimization Status:")
    opt_stats = health['prompt_optimization']
    print(f"  Total Optimizations: {opt_stats.get('total_optimizations', 0)}")
    print(f"  Success Rate: {opt_stats.get('success_rate', 0)*100:.1f}%")
    
    # Configuration status
    print("\n⚙️ Configuration Status:")
    config_status = health['configuration']
    print(f"  AI Providers Configured: {'✅' if config_status['ai_providers_configured'] else '❌'}")
    print(f"  Learning Enabled: {'✅' if config_status['learning_enabled'] else '❌'}")
    print(f"  Cursor Integration: {'✅' if config_status['cursor_integration'] else '❌'}")

async def show_session_status(orchestrator: OrchestratorMain):
    """Display current session status."""
    print("\n📊 Current Session Status")
    print("=" * 50)
    
    status = orchestrator.get_session_status()
    
    if status.get('status') == 'no_active_session':
        print("No active session")
    else:
        print(f"Session ID: {status.get('session_id', 'Unknown')}")
        print(f"Status: {status.get('status', 'Unknown')}")
        print(f"Total Time: {status.get('total_time', 0):.2f} seconds")
        print(f"Success: {'✅' if status.get('success', False) else '❌'}")
        print(f"Request: {status.get('original_request', 'Unknown')}")

async def show_session_history(orchestrator: OrchestratorMain):
    """Display session history."""
    print("\n📜 Session History")
    print("=" * 50)
    
    history = orchestrator.get_session_history()
    
    if not history:
        print("No sessions found")
        return
    
    for session in history:
        print(f"Session: {session['session_id']}")
        print(f"  Request: {session['request_preview']}")
        print(f"  Success: {'✅' if session['success'] else '❌'}")
        print(f"  Time: {session['total_time']:.2f}s")
        print(f"  Timestamp: {session['timestamp']}")
        print()

async def export_session_data(orchestrator: OrchestratorMain, session_id: str):
    """Export session data for a specific session."""
    print(f"\n📤 Exporting Session Data: {session_id}")
    print("=" * 50)
    
    session_data = orchestrator.export_session_data(session_id)
    
    if not session_data:
        print(f"❌ Session {session_id} not found")
        return
    
    # Create export directory
    export_dir = Path("exports")
    export_dir.mkdir(exist_ok=True)
    
    # Export to JSON file
    import json
    export_file = export_dir / f"session_{session_id}.json"
    
    with open(export_file, 'w', encoding='utf-8') as f:
        json.dump(session_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Session data exported to: {export_file}")
    
    # Display summary
    print(f"\n📋 Export Summary:")
    print(f"  Session ID: {session_data['session_id']}")
    print(f"  Request: {session_data['original_request'][:100]}...")
    print(f"  Success: {'✅' if session_data['success'] else '❌'}")
    print(f"  Total Time: {session_data['total_time']:.2f}s")
    print(f"  Optimized Prompts: {len(session_data['optimized_prompts'])}")

async def process_single_request(orchestrator: OrchestratorMain, request: str, verbose: bool):
    """Process a single coding request."""
    print(f"\n🎯 Processing Request: {request}")
    print("=" * 50)
    
    try:
        # Orchestrate the request
        result = await orchestrator.orchestrate_request(request)
        
        if result.success:
            print(f"\n✅ Request processed successfully!")
            print(f"Session ID: {result.session_id}")
            print(f"Total Time: {result.total_time:.2f} seconds")
            
            if verbose:
                print(f"\n📊 Detailed Results:")
                print(f"  Analysis: {result.analysis.complexity_level} complexity")
                print(f"  Project Type: {result.analysis.project_type}")
                print(f"  Technology Stack: {result.analysis.technology_stack}")
                print(f"  Optimized Prompts: {len(result.optimized_prompts)}")
                
                # Show first optimized prompt
                if result.optimized_prompts:
                    first_prompt = result.optimized_prompts[0]
                    print(f"\n🔧 First Optimized Prompt:")
                    print(f"  Task Type: {first_prompt.task_type}")
                    print(f"  Confidence: {first_prompt.confidence_score:.2f}")
                    print(f"  Provider: {first_prompt.provider}")
                    print(f"  Prompt: {first_prompt.optimized_prompt[:200]}...")
        else:
            print(f"\n❌ Request processing failed!")
            print(f"Error: {result.metadata.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"\n❌ Error processing request: {e}")
        if verbose:
            import traceback
            traceback.print_exc()

def print_banner():
    """Print the application banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    AI CODING ORCHESTRATOR                    ║
    ║                                                              ║
    ║  🚀 Intelligent development workflow management             ║
    ║  🔍 Deep request analysis and project planning              ║
    ║  🤖 Multi-AI provider support (OpenAI, Gemini, Claude)      ║
    ║  🔧 AI-optimized prompts for Cursor                         ║
    ║  📚 Continuous learning and improvement                     ║
    ║  📁 File monitoring and validation                          ║
    ║  🔄 Self-correction and error recovery                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

if __name__ == "__main__":
    # Print banner
    print_banner()
    
    # Run the main function
    asyncio.run(main())
