"""
Google Gemini Provider Implementation
Handles communication with Google's Gemini models
"""

import asyncio
from typing import Dict, Any, List
from .base_provider import <PERSON>Provider, AIRequest, AIResponse, TaskType
import google.generativeai as genai

class GeminiProvider(AIProvider):
    """Google Gemini provider implementation"""
    
    def _initialize(self):
        """Initialize Gemini client"""
        try:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-pro')
            self.is_available = True
        except Exception as e:
            self.is_available = False
            print(f"Failed to initialize Gemini provider: {e}")
    
    async def generate_response(self, request: AIRequest) -> AIResponse:
        """Generate response using Google Gemini"""
        try:
            # Prepare the prompt
            full_prompt = request.prompt
            if request.system_message:
                full_prompt = f"{request.system_message}\n\n{request.prompt}"
            
            # Generate response
            response = await asyncio.to_thread(
                self.model.generate_content,
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=request.temperature,
                    max_output_tokens=request.max_tokens or 4000
                )
            )
            
            # Extract content
            content = response.text
            
            # Estimate tokens (Gemini doesn't provide exact token counts)
            estimated_tokens = len(content.split()) * 1.3
            
            # Calculate estimated cost
            cost = self._calculate_cost(estimated_tokens)
            
            return AIResponse(
                content=content,
                provider="gemini",
                model="gemini-pro",
                tokens_used=int(estimated_tokens),
                cost=cost,
                metadata={
                    "prompt_feedback": getattr(response, 'prompt_feedback', None),
                    "candidates": len(response.candidates) if hasattr(response, 'candidates') else 1
                }
            )
            
        except Exception as e:
            raise Exception(f"Gemini API error: {str(e)}")
    
    def get_models(self) -> List[str]:
        """Get available Gemini models"""
        return [
            "gemini-pro",
            "gemini-pro-vision"
        ]
    
    def estimate_cost(self, request: AIRequest) -> float:
        """Estimate cost for the request"""
        # Gemini pricing (approximate)
        estimated_prompt_tokens = len(request.prompt.split()) * 1.3
        estimated_completion_tokens = request.max_tokens or 1000
        
        # Gemini Pro pricing: $0.0005 per 1K characters input, $0.0015 per 1K characters output
        input_cost = (estimated_prompt_tokens * 4) * 0.0005 / 1000  # Rough character estimation
        output_cost = (estimated_completion_tokens * 4) * 0.0015 / 1000
        
        return round(input_cost + output_cost, 4)
    
    def _calculate_cost(self, estimated_tokens: int) -> float:
        """Calculate cost based on estimated tokens"""
        # Convert tokens to characters (rough estimate: 1 token ≈ 4 characters)
        estimated_chars = estimated_tokens * 4
        
        # Gemini Pro pricing
        if estimated_chars <= 1000:  # Input
            cost = estimated_chars * 0.0005 / 1000
        else:  # Output
            cost = estimated_chars * 0.0015 / 1000
        
        return round(cost, 4)
    
    def is_suitable_for_task(self, task_type: TaskType) -> bool:
        """Gemini is particularly good for analysis and planning tasks"""
        return task_type in [
            TaskType.ANALYSIS,
            TaskType.PLANNING,
            TaskType.REVIEW,
            TaskType.CODE_GENERATION
        ]
