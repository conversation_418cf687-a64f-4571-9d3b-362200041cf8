"""
Request Analyzer
Deeply analyzes user requests to understand requirements and generate comprehensive specifications
"""

import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from enum import Enum
from ai_providers import AIProviderManager, AIRequest, TaskType

class RequestComplexity(Enum):
    """Request complexity levels"""
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    ENTERPRISE = "enterprise"

class TechnologyCategory(Enum):
    """Technology categories"""
    FRONTEND = "frontend"
    BACKEND = "backend"
    DATABASE = "database"
    DEVOPS = "devops"
    TESTING = "testing"
    SECURITY = "security"
    MOBILE = "mobile"
    AI_ML = "ai_ml"

@dataclass
class TechnologyRequirement:
    """Technology requirement specification"""
    category: TechnologyCategory
    name: str
    version: Optional[str] = None
    alternatives: List[str] = field(default_factory=list)
    is_required: bool = True
    reason: Optional[str] = None

@dataclass
class ProjectRequirement:
    """Project requirement specification"""
    description: str
    priority: str = "medium"  # high, medium, low
    complexity: str = "medium"
    estimated_time: Optional[str] = None
    dependencies: List[str] = field(default_factory=list)

@dataclass
class RequestAnalysis:
    """Complete analysis of a user request"""
    original_request: str
    summary: str
    complexity: RequestComplexity
    technologies: List[TechnologyRequirement]
    requirements: List[ProjectRequirement]
    estimated_duration: str
    risk_factors: List[str]
    follow_up_questions: List[str]
    development_phases: List[str]
    success_criteria: List[str]

class RequestAnalyzer:
    """Analyzes user requests to create comprehensive project specifications"""
    
    def __init__(self, ai_manager: AIProviderManager):
        self.ai_manager = ai_manager
        
        # Common technology patterns
        self.tech_patterns = {
            TechnologyCategory.FRONTEND: [
                r"react|vue|angular|svelte|html|css|javascript|typescript",
                r"frontend|ui|user.?interface|client.?side|browser"
            ],
            TechnologyCategory.BACKEND: [
                r"python|node\.?js|java|go|rust|c#|\.net|php|ruby",
                r"backend|api|server|serverless|microservices"
            ],
            TechnologyCategory.DATABASE: [
                r"postgresql|mysql|mongodb|redis|sqlite|dynamodb|firebase",
                r"database|db|storage|persistence"
            ],
            TechnologyCategory.DEVOPS: [
                r"docker|kubernetes|aws|azure|gcp|ci.?cd|jenkins|github.?actions",
                r"deployment|hosting|cloud|infrastructure|devops"
            ]
        }
    
    async def analyze_request(self, user_request: str) -> RequestAnalysis:
        """Perform deep analysis of user request"""
        # Initial pattern-based analysis
        initial_analysis = self._pattern_analysis(user_request)
        
        # AI-powered deep analysis
        ai_analysis = await self._ai_analysis(user_request, initial_analysis)
        
        # Combine and refine analysis
        final_analysis = self._combine_analyses(user_request, initial_analysis, ai_analysis)
        
        return final_analysis
    
    def _pattern_analysis(self, request: str) -> Dict[str, Any]:
        """Perform pattern-based analysis of the request"""
        request_lower = request.lower()
        
        # Detect technologies
        detected_techs = []
        for category, patterns in self.tech_patterns.items():
            for pattern in patterns:
                if re.search(pattern, request_lower):
                    detected_techs.append(category)
                    break
        
        # Detect complexity indicators
        complexity_indicators = {
            RequestComplexity.SIMPLE: ["simple", "basic", "hello world", "starter"],
            RequestComplexity.MEDIUM: ["app", "application", "website", "api"],
            RequestComplexity.COMPLEX: ["platform", "system", "enterprise", "scalable"],
            RequestComplexity.ENTERPRISE: ["enterprise", "large.?scale", "distributed", "microservices"]
        }
        
        detected_complexity = RequestComplexity.MEDIUM  # default
        for complexity, indicators in complexity_indicators.items():
            if any(indicator in request_lower for indicator in indicators):
                detected_complexity = complexity
                break
        
        return {
            "detected_technologies": detected_techs,
            "complexity": detected_complexity,
            "has_frontend": TechnologyCategory.FRONTEND in detected_techs,
            "has_backend": TechnologyCategory.BACKEND in detected_techs,
            "has_database": TechnologyCategory.DATABASE in detected_techs
        }
    
    async def _ai_analysis(self, request: str, initial_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Use AI to perform deep analysis of the request"""
        system_message = """You are an expert software architect and project planner. 
        Analyze the user's request and provide a comprehensive breakdown including:
        1. Project summary and scope
        2. Technology recommendations with versions
        3. Development phases and timeline
        4. Risk factors and considerations
        5. Follow-up questions to clarify requirements
        6. Success criteria
        
        Be specific, practical, and provide actionable insights."""
        
        ai_request = AIRequest(
            prompt=f"""Please analyze this software development request:

{request}

Initial analysis shows:
- Technologies detected: {initial_analysis['detected_technologies']}
- Complexity level: {initial_analysis['complexity'].value}
- Has frontend: {initial_analysis['has_frontend']}
- Has backend: {initial_analysis['has_backend']}
- Has database: {initial_analysis['has_database']}

Provide a comprehensive analysis in JSON format with the following structure:
{{
    "summary": "Brief project summary",
    "technologies": [
        {{
            "category": "frontend|backend|database|devops|testing|security|mobile|ai_ml",
            "name": "Technology name",
            "version": "Recommended version",
            "alternatives": ["Alternative 1", "Alternative 2"],
            "is_required": true,
            "reason": "Why this technology is needed"
        }}
    ],
    "requirements": [
        {{
            "description": "Requirement description",
            "priority": "high|medium|low",
            "complexity": "simple|medium|complex",
            "estimated_time": "Time estimate",
            "dependencies": ["Dependency 1", "Dependency 2"]
        }}
    ],
    "estimated_duration": "Overall time estimate",
    "risk_factors": ["Risk 1", "Risk 2"],
    "follow_up_questions": ["Question 1", "Question 2"],
    "development_phases": ["Phase 1", "Phase 2"],
    "success_criteria": ["Criterion 1", "Criterion 2"]
}}""",
            task_type=TaskType.ANALYSIS,
            system_message=system_message,
            max_tokens=2000
        )
        
        try:
            response = await self.ai_manager.generate_response(ai_request)
            # Parse JSON response
            import json
            return json.loads(response.content)
        except Exception as e:
            print(f"AI analysis failed: {e}")
            return self._fallback_analysis(request, initial_analysis)
    
    def _fallback_analysis(self, request: str, initial_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback analysis when AI fails"""
        return {
            "summary": f"Build a {initial_analysis['complexity'].value} application based on the request",
            "technologies": [
                {
                    "category": "frontend",
                    "name": "React" if initial_analysis['has_frontend'] else "None",
                    "version": "18.x",
                    "alternatives": ["Vue", "Angular"],
                    "is_required": initial_analysis['has_frontend'],
                    "reason": "Modern frontend framework for user interface"
                },
                {
                    "category": "backend",
                    "name": "Python FastAPI" if initial_analysis['has_backend'] else "None",
                    "version": "0.104.x",
                    "alternatives": ["Node.js", "Go"],
                    "is_required": initial_analysis['has_backend'],
                    "reason": "Fast, modern backend framework"
                },
                {
                    "category": "database",
                    "name": "PostgreSQL" if initial_analysis['has_database'] else "None",
                    "version": "15.x",
                    "alternatives": ["MySQL", "SQLite"],
                    "is_required": initial_analysis['has_database'],
                    "reason": "Reliable relational database"
                }
            ],
            "requirements": [
                {
                    "description": "Implement core functionality",
                    "priority": "high",
                    "complexity": "medium",
                    "estimated_time": "2-4 weeks",
                    "dependencies": []
                }
            ],
            "estimated_duration": "2-4 weeks",
            "risk_factors": ["Requirements may need clarification"],
            "follow_up_questions": ["What specific features do you need?", "Any performance requirements?"],
            "development_phases": ["Planning", "Development", "Testing", "Deployment"],
            "success_criteria": ["Application works as specified", "Code is maintainable"]
        }
    
    def _combine_analyses(self, request: str, pattern_analysis: Dict[str, Any], ai_analysis: Dict[str, Any]) -> RequestAnalysis:
        """Combine pattern analysis and AI analysis into final result"""
        
        # Convert technologies to TechnologyRequirement objects
        technologies = []
        for tech in ai_analysis.get("technologies", []):
            try:
                category = TechnologyCategory(tech["category"])
                technologies.append(TechnologyRequirement(
                    category=category,
                    name=tech["name"],
                    version=tech.get("version"),
                    alternatives=tech.get("alternatives", []),
                    is_required=tech.get("is_required", True),
                    reason=tech.get("reason")
                ))
            except ValueError:
                continue
        
        # Convert requirements to ProjectRequirement objects
        requirements = []
        for req in ai_analysis.get("requirements", []):
            requirements.append(ProjectRequirement(
                description=req["description"],
                priority=req.get("priority", "medium"),
                complexity=req.get("complexity", "medium"),
                estimated_time=req.get("estimated_time"),
                dependencies=req.get("dependencies", [])
            ))
        
        return RequestAnalysis(
            original_request=request,
            summary=ai_analysis.get("summary", "Project development request"),
            complexity=pattern_analysis["complexity"],
            technologies=technologies,
            requirements=requirements,
            estimated_duration=ai_analysis.get("estimated_duration", "Unknown"),
            risk_factors=ai_analysis.get("risk_factors", []),
            follow_up_questions=ai_analysis.get("follow_up_questions", []),
            development_phases=ai_analysis.get("development_phases", []),
            success_criteria=ai_analysis.get("success_criteria", [])
        )
