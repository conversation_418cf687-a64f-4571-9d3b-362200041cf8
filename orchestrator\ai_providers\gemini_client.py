"""
Google Gemini client implementation for the AI Coding Orchestrator.
"""

import asyncio
import google.generativeai as genai
from typing import Dict, Any, List
from .base_provider import AIProvider, AIRequest, AIResponse, TaskType, RateLimitError

class GeminiClient(AIProvider):
    """Google Gemini API client implementation."""
    
    def _initialize(self) -> None:
        """Initialize Gemini client."""
        try:
            genai.configure(api_key=self.api_key)
            # Prefer widely available, low-latency model; fallback to legacy id
            try:
                self.model = genai.GenerativeModel('gemini-1.5-flash')
            except Exception:
                # Fallback for older accounts/regions
                self.model = genai.GenerativeModel('gemini-pro')
            self.is_available = True
        except Exception as e:
            print(f"Failed to initialize Gemini client: {e}")
            self.is_available = False
    
    async def generate_response(self, request: AIRequest) -> AIResponse:
        """Generate response using Gemini API."""
        if not self.is_available:
            raise Exception("Gemini client is not available")
        
        try:
            # Prepare prompt with context
            full_prompt = self._prepare_prompt(request)
            
            # Make API call
            response = await asyncio.to_thread(
                self.model.generate_content,
                full_prompt,
                generation_config=self._get_generation_config(request)
            )
            
            # Extract response content
            content = response.text
            
            # Calculate cost (approximate)
            cost = self._calculate_cost(len(full_prompt), len(content))
            
            # Determine model id if available on response
            model_id = None
            try:
                model_id = getattr(getattr(response, 'model_version', None), 'name', None) or 'gemini-1.5-flash'
            except Exception:
                model_id = 'gemini-1.5-flash'

            return AIResponse(
                content=content,
                provider="Google Gemini",
                model=model_id,
                tokens_used=len(full_prompt) + len(content),  # Rough estimation
                cost=cost,
                metadata={
                    "finish_reason": getattr(response, 'finish_reason', None),
                    "candidates": len(response.candidates) if hasattr(response, 'candidates') else 1
                }
            )
            
        except Exception as e:
            # Detect quota/429 and attempt in-provider fallback once
            message = str(e)
            if "429" in message or "quota" in message.lower():
                # Try alternative lightweight model variant
                try:
                    alt_model = None
                    try:
                        alt_model = genai.GenerativeModel('gemini-1.5-flash-8b')
                    except Exception:
                        alt_model = genai.GenerativeModel('gemini-pro')
                    response = await asyncio.to_thread(
                        alt_model.generate_content,
                        full_prompt,
                        generation_config={"max_output_tokens": 128, "temperature": 0.6}
                    )
                    content = response.text
                    cost = self._calculate_cost(len(full_prompt), len(content))
                    model_id = 'gemini-1.5-flash-8b'
                    return AIResponse(
                        content=content,
                        provider="Google Gemini",
                        model=model_id,
                        tokens_used=len(full_prompt) + len(content),
                        cost=cost,
                        metadata={
                            "finish_reason": getattr(response, 'finish_reason', None),
                            "candidates": len(response.candidates) if hasattr(response, 'candidates') else 1,
                            "fallback_model": True
                        }
                    )
                except Exception:
                    # Parse retry delay if present
                    retry_after = 30.0
                    try:
                        import re
                        m = re.search(r"retry_delay\s*\{\s*seconds:\s*(\d+)", message)
                        if m:
                            retry_after = float(m.group(1))
                    except Exception:
                        pass
                    raise RateLimitError("Gemini rate limit/quota reached", retry_after_seconds=retry_after)
            raise Exception(f"Gemini API error: {e}")
    
    def _prepare_prompt(self, request: AIRequest) -> str:
        """Prepare the full prompt with context."""
        prompt_parts = []
        
        # Add system context if provided
        if request.context and "system_prompt" in request.context:
            prompt_parts.append(f"System: {request.context['system_prompt']}")
        
        # Add main prompt
        prompt_parts.append(request.prompt)
        
        # Add additional context if provided
        if request.context and "additional_context" in request.context:
            prompt_parts.append(f"\nAdditional context: {request.context['additional_context']}")
        
        return "\n\n".join(prompt_parts)
    
    def _get_generation_config(self, request: AIRequest) -> Dict[str, Any]:
        """Get generation configuration for Gemini."""
        config = {}
        
        if request.max_tokens:
            config['max_output_tokens'] = request.max_tokens
        else:
            # Keep outputs short to reduce quota usage
            config['max_output_tokens'] = 256
        
        if request.temperature is not None:
            config['temperature'] = request.temperature
        else:
            config['temperature'] = 0.7
        
        return config

    async def test_connection(self) -> bool:
        """Lightweight connectivity check that avoids consuming generation quota."""
        try:
            # Listing models should not consume generation quota
            models = await asyncio.to_thread(lambda: list(genai.list_models()))
            self.is_available = len(models) > 0
            return self.is_available
        except Exception:
            self.is_available = False
            return False
    
    def _calculate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Calculate approximate cost for the request."""
        # Gemini Pro pricing (approximate)
        input_cost = (input_tokens / 1000) * 0.0005   # $0.0005 per 1K input tokens
        output_cost = (output_tokens / 1000) * 0.0015 # $0.0015 per 1K output tokens
        
        return input_cost + output_cost
    
    def get_models(self) -> List[str]:
        """Get available Gemini models."""
        return [
            "gemini-1.5-pro",
            "gemini-1.5-flash",
            "gemini-2.0-pro",
            "gemini-2.0-flash"
        ]
    
    def estimate_cost(self, request: AIRequest) -> float:
        """Estimate cost for a request."""
        # Rough estimation based on prompt length
        estimated_input = len(request.prompt.split()) * 1.3
        estimated_output = 500  # Default output estimation
        return self._calculate_cost(int(estimated_input), estimated_output)
    
    def is_suitable_for_task(self, task_type: TaskType) -> bool:
        """Check if Gemini is suitable for the task."""
        # Gemini excels at analysis and planning tasks
        suitable_tasks = [
            TaskType.ANALYSIS,
            TaskType.PLANNING,
            TaskType.REVIEW
        ]
        return task_type in suitable_tasks
