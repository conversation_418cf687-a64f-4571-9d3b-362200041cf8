#!/usr/bin/env python3
"""
Example script demonstrating the AI Coding Orchestrator.
"""

import asyncio
import sys
from pathlib import Path

# Add the orchestrator package to the path
sys.path.insert(0, str(Path(__file__).parent))

from orchestrator.orchestrator_main import OrchestratorMain

async def main():
    """Demonstrate the AI Coding Orchestrator."""
    print("🚀 AI Coding Orchestrator - Example Demo")
    print("=" * 60)
    
    # Initialize the orchestrator
    orchestrator = OrchestratorMain()
    
    # Initialize the system
    print("\n🔧 Initializing system...")
    if not await orchestrator.initialize():
        print("❌ Failed to initialize orchestrator.")
        return
    
    print("✅ System initialized successfully!")
    
    # Example 1: Simple request
    print("\n" + "="*60)
    print("📝 Example 1: Simple Todo App Request")
    print("="*60)
    
    request1 = "Build me a todo app with React frontend and Python FastAPI backend"
    print(f"Request: {request1}")
    
    result1 = await orchestrator.orchestrate_request(request1)
    
    if result1.success:
        print(f"✅ Request completed successfully!")
        print(f"Session ID: {result1.session_id}")
        print(f"Total Time: {result1.total_time:.2f} seconds")
        print(f"Complexity: {result1.analysis.complexity_level}")
        print(f"Project Type: {result1.analysis.project_type}")
        print(f"Technology Stack: {result1.analysis.technology_stack}")
        print(f"Optimized Prompts: {len(result1.optimized_prompts)}")
    else:
        print(f"❌ Request failed: {result1.metadata.get('error', 'Unknown error')}")
    
    # Example 2: Complex request
    print("\n" + "="*60)
    print("📝 Example 2: Complex E-commerce Platform Request")
    print("="*60)
    
    request2 = """Build me a full-stack e-commerce platform with:
    - React frontend with TypeScript
    - Node.js backend with Express
    - PostgreSQL database with Redis caching
    - User authentication and authorization
    - Product catalog and search
    - Shopping cart and checkout
    - Payment integration with Stripe
    - Admin dashboard for inventory management
    - Docker containerization
    - CI/CD pipeline with GitHub Actions"""
    
    print(f"Request: {request2[:100]}...")
    
    result2 = await orchestrator.orchestrate_request(request2)
    
    if result2.success:
        print(f"✅ Request completed successfully!")
        print(f"Session ID: {result2.session_id}")
        print(f"Total Time: {result2.total_time:.2f} seconds")
        print(f"Complexity: {result2.analysis.complexity_level}")
        print(f"Project Type: {result2.analysis.project_type}")
        print(f"Technology Stack: {result2.analysis.technology_stack}")
        print(f"Optimized Prompts: {len(result2.optimized_prompts)}")
    else:
        print(f"❌ Request failed: {result2.metadata.get('error', 'Unknown error')}")
    
    # Show system statistics
    print("\n" + "="*60)
    print("📊 System Statistics")
    print("="*60)
    
    health = orchestrator.get_system_health()
    print(f"Orchestrator Status: {health['orchestrator_status']}")
    print(f"AI Providers: {len(health['ai_providers'])} configured")
    
    # AI Provider details
    print("\n🤖 AI Provider Status:")
    for provider_name, provider_info in health['ai_providers'].items():
        status = "✅ Available" if provider_info.get('available', False) else "❌ Unavailable"
        print(f"  {provider_name}: {status}")
        if 'stats' in provider_info:
            stats = provider_info['stats']
            print(f"    Requests: {stats.get('total_requests', 0)}")
            if stats.get('total_requests', 0) > 0:
                success_rate = stats.get('successful_requests', 0) / stats.get('total_requests', 1) * 100
                print(f"    Success Rate: {success_rate:.1f}%")
    
    # Learning system status
    print("\n🧠 Learning System Status:")
    learning_stats = health['learning_system']
    print(f"  Total Attempts: {learning_stats.get('total_attempts', 0)}")
    print(f"  Success Rate: {learning_stats.get('success_rate', 0)*100:.1f}%")
    print(f"  Average Confidence: {learning_stats.get('average_confidence', 0):.2f}")
    
    # Session history
    print("\n📜 Session History:")
    history = orchestrator.get_session_history()
    for session in history:
        print(f"  {session['session_id']}: {session['request_preview']} - {'✅' if session['success'] else '❌'}")
    
    # Cleanup
    print("\n🧹 Cleaning up...")
    await orchestrator.cleanup()
    
    print("\n🎉 Demo completed successfully!")
    print("\nTo use the orchestrator:")
    print("  python main.py 'Your coding request here'")
    print("  python main.py --interactive")
    print("  python main.py --health")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
