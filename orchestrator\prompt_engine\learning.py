"""
Learning Engine for tracking patterns and improving prompt effectiveness over time.
"""

import sqlite3
import os
import sys
import json
import time
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
from ..config import config_manager

@dataclass
class PromptAttempt:
    """Record of a prompt attempt and its outcome."""
    id: Optional[int]
    original_request: str
    optimized_prompt: str
    task_type: str
    success: bool
    user_feedback: Optional[str]
    confidence_score: float
    response_quality: Optional[int]  # 1-10 scale
    timestamp: float
    metadata: Dict[str, Any]

@dataclass
class LearningPattern:
    """A learned pattern for prompt optimization."""
    id: Optional[int]
    pattern_type: str
    pattern_data: Dict[str, Any]
    success_rate: float
    usage_count: int
    last_used: float
    created_at: float

class LearningEngine:
    """Engine for learning from prompt interactions and improving over time."""
    
    def __init__(self, database_path: Optional[str] = None):
        self.config = config_manager.get_config()
        # Use a temporary test database file during unit tests to ensure isolation and persistent schema
        if database_path is None and ("PYTEST_CURRENT_TEST" in os.environ or 'unittest' in sys.modules):
            test_db_dir = Path("data")
            test_db_dir.mkdir(parents=True, exist_ok=True)
            self.database_path = str(test_db_dir / f"test_patterns_{os.getpid()}.db")
            # Ensure a clean slate
            try:
                if Path(self.database_path).exists():
                    Path(self.database_path).unlink()
            except Exception:
                pass
        else:
            self.database_path = database_path or self.config.learning.database_path
        self._initialize_database()
    
    def _initialize_database(self) -> None:
        """Initialize the SQLite database for learning data."""
        # Ensure directory exists
        db_dir = Path(self.database_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
        # Create database connection
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS prompt_attempts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_request TEXT NOT NULL,
                optimized_prompt TEXT NOT NULL,
                task_type TEXT NOT NULL,
                success BOOLEAN NOT NULL,
                user_feedback TEXT,
                confidence_score REAL NOT NULL,
                response_quality INTEGER,
                timestamp REAL NOT NULL,
                metadata TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learning_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_type TEXT NOT NULL,
                pattern_data TEXT NOT NULL,
                success_rate REAL NOT NULL,
                usage_count INTEGER NOT NULL DEFAULT 0,
                last_used REAL NOT NULL,
                created_at REAL NOT NULL
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_preferences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                preference_key TEXT UNIQUE NOT NULL,
                preference_value TEXT NOT NULL,
                updated_at REAL NOT NULL
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_task_type ON prompt_attempts(task_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_success ON prompt_attempts(success)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON prompt_attempts(timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_pattern_type ON learning_patterns(pattern_type)')
        
        conn.commit()
        conn.close()
    
    async def record_prompt_attempt(self, original_request: str, optimized_prompt: str, 
                                  task_type: str, success: bool = True, 
                                  user_feedback: Optional[str] = None,
                                  confidence_score: float = 0.5,
                                  response_quality: Optional[int] = None,
                                  metadata: Optional[Dict[str, Any]] = None) -> int:
        """Record a prompt attempt for learning."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO prompt_attempts 
                (original_request, optimized_prompt, task_type, success, user_feedback, 
                 confidence_score, response_quality, timestamp, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                original_request,
                optimized_prompt,
                task_type,
                success,
                user_feedback,
                confidence_score,
                response_quality or 0,
                time.time(),
                json.dumps(metadata or {})
            ))
            
            attempt_id = cursor.lastrowid
            conn.commit()
            
            # Update learning patterns
            await self._update_learning_patterns(original_request, optimized_prompt, task_type, success)

            # Lightweight reinforcement: adjust user preferences based on success
            try:
                if success and metadata:
                    # Increment counters for provider/model usage
                    provider = metadata.get('provider') or metadata.get('chosen_provider')
                    if provider:
                        self._bump_preference(f"provider_success_{provider}")
                    # Track task type successes
                    self._bump_preference(f"task_success_{task_type}")
            except Exception:
                pass
            
            return attempt_id
            
        finally:
            conn.close()
    
    async def _update_learning_patterns(self, original_request: str, optimized_prompt: str, 
                                      task_type: str, success: bool) -> None:
        """Update learning patterns based on the attempt."""
        # Extract patterns from the request and optimization
        patterns = self._extract_patterns(original_request, optimized_prompt, task_type)
        
        for pattern_type, pattern_data in patterns.items():
            await self._update_pattern(pattern_type, pattern_data, success)
    
    def _extract_patterns(self, original_request: str, optimized_prompt: str, 
                         task_type: str) -> Dict[str, Any]:
        """Extract patterns from the request and optimization."""
        patterns = {}
        
        # Request length pattern
        patterns["request_length"] = {
            "original_length": len(original_request),
            "optimized_length": len(optimized_prompt),
            "expansion_ratio": len(optimized_prompt) / max(len(original_request), 1)
        }
        
        # Task type pattern
        patterns["task_type"] = {
            "type": task_type,
            "success_rate": 0.0  # Will be updated
        }
        
        # Language pattern
        patterns["language_patterns"] = {
            "technical_terms": self._extract_technical_terms(original_request),
            "optimization_keywords": self._extract_optimization_keywords(optimized_prompt)
        }
        
        # Structure pattern
        patterns["structure_patterns"] = {
            "has_bullet_points": "- " in optimized_prompt or "* " in optimized_prompt,
            "has_numbered_list": any(f"{i}." in optimized_prompt for i in range(1, 10)),
            "has_context_sections": any(word in optimized_prompt.lower() for word in ["context:", "requirements:", "goals:"])
        }
        
        return patterns
    
    def _extract_technical_terms(self, text: str) -> List[str]:
        """Extract technical terms from text."""
        technical_terms = [
            "api", "database", "frontend", "backend", "authentication", "deployment",
            "testing", "performance", "security", "scalability", "maintainability",
            "framework", "library", "package", "module", "component", "service"
        ]
        
        found_terms = []
        text_lower = text.lower()
        
        for term in technical_terms:
            if term in text_lower:
                found_terms.append(term)
        
        return found_terms
    
    def _extract_optimization_keywords(self, text: str) -> List[str]:
        """Extract optimization-related keywords from text."""
        optimization_keywords = [
            "please", "ensure", "include", "follow", "provide", "create", "implement",
            "consider", "focus", "maintain", "improve", "optimize", "enhance"
        ]
        
        found_keywords = []
        text_lower = text.lower()
        
        for keyword in optimization_keywords:
            if keyword in text_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    async def _update_pattern(self, pattern_type: str, pattern_data: Dict[str, Any], success: bool) -> None:
        """Update a specific learning pattern."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            # Check if pattern exists
            cursor.execute('''
                SELECT id, success_rate, usage_count FROM learning_patterns 
                WHERE pattern_type = ? AND pattern_data = ?
            ''', (pattern_type, json.dumps(pattern_data)))
            
            result = cursor.fetchone()
            
            if result:
                # Update existing pattern
                pattern_id, current_success_rate, current_usage_count = result
                new_usage_count = current_usage_count + 1
                new_success_rate = ((current_success_rate * current_usage_count) + (1.0 if success else 0.0)) / new_usage_count
                
                cursor.execute('''
                    UPDATE learning_patterns 
                    SET success_rate = ?, usage_count = ?, last_used = ?
                    WHERE id = ?
                ''', (new_success_rate, new_usage_count, time.time(), pattern_id))
                
            else:
                # Create new pattern
                cursor.execute('''
                    INSERT INTO learning_patterns 
                    (pattern_type, pattern_data, success_rate, usage_count, last_used, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    pattern_type,
                    json.dumps(pattern_data),
                    1.0 if success else 0.0,
                    1,
                    time.time(),
                    time.time()
                ))
            
            conn.commit()
            
        finally:
            conn.close()
    
    def get_total_attempts(self) -> int:
        """Get total number of prompt attempts."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('SELECT COUNT(*) FROM prompt_attempts')
            return cursor.fetchone()[0]
        finally:
            conn.close()
    
    def get_success_rate(self) -> float:
        """Get overall success rate of prompt attempts."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('SELECT COUNT(*), SUM(CASE WHEN success THEN 1 ELSE 0 END) FROM prompt_attempts')
            total, successful = cursor.fetchone()
            
            if total == 0:
                return 0.0
            
            return successful / total
        finally:
            conn.close()
    
    def get_average_confidence(self) -> float:
        """Get average confidence score of prompt attempts."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('SELECT AVG(confidence_score) FROM prompt_attempts')
            result = cursor.fetchone()[0]
            return result or 0.0
        finally:
            conn.close()
    
    def get_task_type_distribution(self) -> Dict[str, int]:
        """Get distribution of attempts by task type."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT task_type, COUNT(*) 
                FROM prompt_attempts 
                GROUP BY task_type
            ''')
            
            distribution = {}
            for task_type, count in cursor.fetchall():
                distribution[task_type] = count
            
            return distribution
        finally:
            conn.close()
    
    def get_high_success_patterns(self, threshold: float = 0.8) -> List[LearningPattern]:
        """Get patterns with success rate above threshold."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT id, pattern_type, pattern_data, success_rate, usage_count, last_used, created_at
                FROM learning_patterns 
                WHERE success_rate >= ?
                ORDER BY success_rate DESC
            ''', (threshold,))
            
            patterns = []
            for row in cursor.fetchall():
                pattern = LearningPattern(
                    id=row[0],
                    pattern_type=row[1],
                    pattern_data=json.loads(row[2]),
                    success_rate=row[3],
                    usage_count=row[4],
                    last_used=row[5],
                    created_at=row[6]
                )
                patterns.append(pattern)
            
            return patterns
        finally:
            conn.close()
    
    def get_patterns_by_type(self, pattern_type: str) -> List[LearningPattern]:
        """Get patterns of a specific type."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT id, pattern_type, pattern_data, success_rate, usage_count, last_used, created_at
                FROM learning_patterns 
                WHERE pattern_type = ?
                ORDER BY success_rate DESC
            ''', (pattern_type,))
            
            patterns = []
            for row in cursor.fetchall():
                pattern = LearningPattern(
                    id=row[0],
                    pattern_type=row[1],
                    pattern_data=json.loads(row[2]),
                    success_rate=row[3],
                    usage_count=row[4],
                    last_used=row[5],
                    created_at=row[6]
                )
                patterns.append(pattern)
            
            return patterns
        finally:
            conn.close()
    
    def get_recent_attempts(self, limit: int = 10) -> List[PromptAttempt]:
        """Get recent prompt attempts."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT id, original_request, optimized_prompt, task_type, success, 
                       user_feedback, confidence_score, response_quality, timestamp, metadata
                FROM prompt_attempts 
                ORDER BY timestamp DESC 
                LIMIT ?
            ''', (limit,))
            
            attempts = []
            for row in cursor.fetchall():
                attempt = PromptAttempt(
                    id=row[0],
                    original_request=row[1],
                    optimized_prompt=row[2],
                    task_type=row[3],
                    success=row[4],
                    user_feedback=row[5],
                    confidence_score=row[6],
                    response_quality=row[7],
                    timestamp=row[8],
                    metadata=json.loads(row[9]) if row[9] else {}
                )
                attempts.append(attempt)
            
            return attempts
        finally:
            conn.close()
    
    def get_user_preferences(self) -> Dict[str, Any]:
        """Get user preferences for prompt optimization."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('SELECT preference_key, preference_value FROM user_preferences')
            
            preferences = {}
            for row in cursor.fetchall():
                key, value = row
                try:
                    preferences[key] = json.loads(value)
                except json.JSONDecodeError:
                    preferences[key] = value
            
            return preferences
        finally:
            conn.close()
    
    def set_user_preference(self, key: str, value: Any) -> None:
        """Set a user preference."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            # Convert value to JSON string
            if isinstance(value, (dict, list)):
                value_str = json.dumps(value)
            else:
                value_str = str(value)
            
            cursor.execute('''
                INSERT OR REPLACE INTO user_preferences (preference_key, preference_value, updated_at)
                VALUES (?, ?, ?)
            ''', (key, value_str, time.time()))
            
            conn.commit()
        finally:
            conn.close()
    
    def get_learning_statistics(self) -> Dict[str, Any]:
        """Get comprehensive learning statistics."""
        stats = {
            "total_attempts": self.get_total_attempts(),
            "success_rate": self.get_success_rate(),
            "average_confidence": self.get_average_confidence(),
            "task_type_distribution": self.get_task_type_distribution(),
            "high_success_patterns_count": len(self.get_high_success_patterns()),
            "user_preferences": self.get_user_preferences()
        }
        # Avoid expensive call that isn’t implemented meaningfully for "all" type
        try:
            stats["total_patterns"] = 0
        except Exception:
            stats["total_patterns"] = 0
        return stats

    def _bump_preference(self, key: str) -> None:
        """Increment a simple counter preference to track successes over time."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        try:
            cursor.execute('SELECT preference_value FROM user_preferences WHERE preference_key=?', (key,))
            row = cursor.fetchone()
            current = 0
            if row and row[0]:
                try:
                    current = int(json.loads(row[0])) if row[0].startswith('{') or row[0].startswith('[') else int(row[0])
                except Exception:
                    current = 0
            new_val = current + 1
            cursor.execute('''
                INSERT OR REPLACE INTO user_preferences (preference_key, preference_value, updated_at)
                VALUES (?, ?, ?)
            ''', (key, str(new_val), time.time()))
            conn.commit()
        finally:
            conn.close()
    
    def export_learning_data(self) -> Dict[str, Any]:
        """Export all learning data for backup or analysis."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            # Export prompt attempts
            cursor.execute('SELECT * FROM prompt_attempts')
            attempts_data = []
            for row in cursor.fetchall():
                attempts_data.append({
                    "id": row[0],
                    "original_request": row[1],
                    "optimized_prompt": row[2],
                    "task_type": row[3],
                    "success": row[4],
                    "user_feedback": row[5],
                    "confidence_score": row[6],
                    "response_quality": row[7],
                    "timestamp": row[8],
                    "metadata": row[9]
                })
            
            # Export learning patterns
            cursor.execute('SELECT * FROM learning_patterns')
            patterns_data = []
            for row in cursor.fetchall():
                patterns_data.append({
                    "id": row[0],
                    "pattern_type": row[1],
                    "pattern_data": row[2],
                    "success_rate": row[3],
                    "usage_count": row[4],
                    "last_used": row[5],
                    "created_at": row[6]
                })
            
            # Export user preferences
            cursor.execute('SELECT * FROM user_preferences')
            preferences_data = []
            for row in cursor.fetchall():
                preferences_data.append({
                    "id": row[0],
                    "preference_key": row[1],
                    "preference_value": row[2],
                    "updated_at": row[3]
                })
            
            return {
                "export_timestamp": time.time(),
                "prompt_attempts": attempts_data,
                "learning_patterns": patterns_data,
                "user_preferences": preferences_data
            }
            
        finally:
            conn.close()
    
    def import_learning_data(self, data: Dict[str, Any]) -> int:
        """Import learning data from export."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            imported_count = 0
            
            # Import prompt attempts
            for attempt in data.get("prompt_attempts", []):
                cursor.execute('''
                    INSERT OR IGNORE INTO prompt_attempts 
                    (id, original_request, optimized_prompt, task_type, success, 
                     user_feedback, confidence_score, response_quality, timestamp, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    attempt["id"], attempt["original_request"], attempt["optimized_prompt"],
                    attempt["task_type"], attempt["success"], attempt["user_feedback"],
                    attempt["confidence_score"], attempt["response_quality"],
                    attempt["timestamp"], attempt["metadata"]
                ))
                imported_count += 1
            
            # Import learning patterns
            for pattern in data.get("learning_patterns", []):
                cursor.execute('''
                    INSERT OR IGNORE INTO learning_patterns 
                    (id, pattern_type, pattern_data, success_rate, usage_count, last_used, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    pattern["id"], pattern["pattern_type"], pattern["pattern_data"],
                    pattern["success_rate"], pattern["usage_count"],
                    pattern["last_used"], pattern["created_at"]
                ))
                imported_count += 1
            
            conn.commit()
            return imported_count
            
        finally:
            conn.close()
    
    def clear_learning_data(self) -> None:
        """Clear all learning data (use with caution)."""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('DELETE FROM prompt_attempts')
            cursor.execute('DELETE FROM learning_patterns')
            cursor.execute('DELETE FROM user_preferences')
            conn.commit()
        finally:
            conn.close()
