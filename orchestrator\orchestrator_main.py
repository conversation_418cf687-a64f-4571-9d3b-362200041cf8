"""
Main Orchestrator class that coordinates all components of the AI Coding Orchestrator.
"""

import asyncio
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from pathlib import Path

from .config import config_manager
from .ai_providers.provider_manager import AIProviderManager
from .analyzers.request_analyzer import Request<PERSON><PERSON>y<PERSON>, RequestAnalysis
from .analyzers.project_planner import ProjectPlanner
from .prompt_engine.optimizer import PromptOptimizer, OptimizedPrompt
from .prompt_engine.learning import LearningEngine
from .file_manager.integrator import CursorIntegrator

@dataclass
class OrchestrationResult:
    """Result of an orchestration session."""
    session_id: str
    original_request: str
    analysis: RequestAnalysis
    project_plan: Dict[str, Any]
    optimized_prompts: List[OptimizedPrompt]
    execution_status: str
    total_time: float
    success: bool
    metadata: Dict[str, Any]
    # Keep the start time to inform history/status
    session_start_time: float = 0.0

class OrchestratorMain:
    """Main orchestrator that coordinates the entire AI coding workflow."""
    
    def __init__(self):
        self.config = config_manager.get_config()
        self.session_id = None
        self.session_start_time = None
        
        # Initialize components
        self.ai_provider_manager = AIProviderManager()
        # Propagate verbosity preference
        try:
            self.ai_provider_manager.set_verbose(bool(self.config.verbose))
        except Exception:
            pass
        self.learning_engine = LearningEngine()
        self.request_analyzer = RequestAnalyzer(self.ai_provider_manager)
        self.project_planner = ProjectPlanner(self.ai_provider_manager)
        self.prompt_optimizer = PromptOptimizer(self.ai_provider_manager, self.learning_engine)
        self.cursor_integrator = CursorIntegrator(self.config)
        
        # Session state
        self.current_session = None
        self.session_history = []
    
    async def initialize(self) -> bool:
        """Initialize the orchestrator and test all components."""
        print("🚀 Initializing AI Coding Orchestrator...")
        
        try:
            # Test AI providers
            print("Testing AI providers...")
            provider_results = await self.ai_provider_manager.test_all_providers()
            
            if not any(provider_results.values()):
                print("⚠️ No AI providers are available. Continuing in offline/heuristic mode.")
            
            # Validate configuration
            from .config import config_manager
            if not config_manager.validate_config():
                print("⚠️ Configuration validation failed, but continuing...")
            
            # Initialize learning system
            print("Initializing learning system...")
            # Learning system is initialized in constructor
            
            print("✅ Orchestrator initialized successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            return False
    
    async def orchestrate_request(self, user_request: str, context: Dict[str, Any] = None) -> OrchestrationResult:
        """Main orchestration method for handling user requests."""
        # Start new session
        self.session_id = f"session_{int(time.time())}"
        self.session_start_time = time.time()
        
        print(f"\n🎯 Starting orchestration session: {self.session_id}")
        print(f"📝 User request: {user_request[:100]}...")
        print("🧭 Plan: analyze → plan → optimize → integrate")
        
        try:
            # Step 1: Deep Request Analysis
            print("\n🔍 Step 1: Analyzing request...")
            analysis = await self.request_analyzer.analyze_request(user_request)
            
            # Display analysis results
            self._display_analysis(analysis)
            
            # Step 2: Project Planning
            print("\n📋 Step 2: Planning project...")
            project_plan = await self.project_planner.plan_project(analysis)
            
            # Display project plan
            self._display_project_plan(project_plan)
            
            # Step 3: Prompt Optimization
            print("\n🔧 Step 3: Optimizing prompts...")
            optimized_prompts = await self._generate_optimized_prompts(analysis, project_plan, context)
            # Learn from the analysis-planning sequence as a generic success attempt
            try:
                if optimized_prompts:
                    await self.learning_engine.record_prompt_attempt(
                        original_request=user_request,
                        optimized_prompt=optimized_prompts[0].optimized_prompt,
                        task_type="orchestration",
                        success=True,
                        confidence_score=0.7,
                        metadata={"phase": "optimize", "project_type": analysis.project_type}
                    )
            except Exception:
                pass
            
            # Step 4: Cursor Integration
            print("\n🎯 Step 4: Integrating with Cursor...")
            integration_result = await self._integrate_with_cursor(optimized_prompts, project_plan)
            # Record integration attempt
            try:
                await self.learning_engine.record_prompt_attempt(
                    original_request=user_request,
                    optimized_prompt=optimized_prompts[0].optimized_prompt if optimized_prompts else user_request,
                    task_type="integration",
                    success=True,
                    confidence_score=0.6,
                    metadata={"phase": "integration", **(integration_result or {})}
                )
            except Exception:
                pass
            
            # Calculate total time
            total_time = time.time() - self.session_start_time
            
            # Create result
            result = OrchestrationResult(
                session_id=self.session_id,
                original_request=user_request,
                analysis=analysis,
                project_plan=project_plan,
                optimized_prompts=optimized_prompts,
                execution_status="completed",
                total_time=total_time,
                success=True,
                metadata={
                    "provider_stats": self.ai_provider_manager.get_provider_stats(),
                    "learning_stats": self.learning_engine.get_learning_statistics(),
                    "integration_result": integration_result
                },
                session_start_time=self.session_start_time
            )
            
            # Store session
            self.current_session = result
            self.session_history.append(result)
            
            print(f"\n✅ Orchestration completed successfully in {total_time:.2f} seconds!")
            return result
            
        except Exception as e:
            print(f"\n❌ Orchestration failed: {e}")
            
            # Create error result
            error_result = OrchestrationResult(
                session_id=self.session_id,
                original_request=user_request,
                analysis=RequestAnalysis(
                    original_request=user_request,
                    identified_components=[],
                    complexity_level="unknown",
                    estimated_time="unknown",
                    risk_factors=[f"Orchestration error: {e}"],
                    follow_up_questions=[],
                    technology_stack={},
                    project_type="unknown",
                    requirements=[],
                    constraints=[]
                ),
                project_plan={},
                optimized_prompts=[],
                execution_status="failed",
                total_time=time.time() - self.session_start_time,
                success=False,
                metadata={"error": str(e)},
                session_start_time=self.session_start_time
            )
            
            return error_result
    
    async def _generate_optimized_prompts(self, analysis: RequestAnalysis, 
                                        project_plan: Dict[str, Any], 
                                        context: Dict[str, Any]) -> List[OptimizedPrompt]:
        """Generate optimized prompts for different development phases."""
        prompts = []
        
        # Generate prompt for project setup
        setup_prompt = await self.prompt_optimizer.optimize_prompt(
            "Set up the project structure and initialize the development environment",
            {
                **(context or {}),
                "project_type": analysis.project_type,
                "technology_stack": analysis.technology_stack,
                "complexity": analysis.complexity_level
            }
        )
        prompts.append(setup_prompt)
        
        # Generate prompts for each development phase
        if "development_roadmap" in project_plan:
            roadmap = project_plan["development_roadmap"]
            if hasattr(roadmap, 'phases') and roadmap.phases:
                for phase in roadmap.phases:
                    if hasattr(phase, 'name') and hasattr(phase, 'description'):
                        phase_prompt = await self.prompt_optimizer.optimize_prompt(
                            f"Execute the {phase.name} phase: {phase.description}",
                            {
                                **(context or {}),
                                "phase": phase,
                                "project_context": project_plan.get("project_structure", {}),
                                "technology_stack": analysis.technology_stack
                            }
                        )
                        prompts.append(phase_prompt)
        
        return prompts
    
    async def _integrate_with_cursor(self, prompts: List[OptimizedPrompt], 
                                   project_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Integrate the optimized prompts with Cursor."""
        try:
            # Create instruction file for Cursor
            instruction_file = await self.cursor_integrator.create_instruction_file(prompts, project_plan)
            
            # Copy prompts to clipboard if enabled
            if self.config.cursor.clipboard_integration:
                await self.cursor_integrator.copy_to_clipboard(prompts[0].optimized_prompt)
            
            return {
                "instruction_file_created": bool(instruction_file),
                "clipboard_updated": self.config.cursor.clipboard_integration,
                "file_monitoring_enabled": self.config.cursor.file_monitoring
            }
            
        except Exception as e:
            print(f"Cursor integration failed: {e}")
            return {"error": str(e)}
    
    def _display_analysis(self, analysis: RequestAnalysis) -> None:
        """Display the request analysis results."""
        print(f"\n📊 REQUEST ANALYSIS RESULTS:")
        print(f"  Project Type: {analysis.project_type}")
        print(f"  Complexity: {analysis.complexity_level}")
        print(f"  Estimated Time: {analysis.estimated_time}")
        print(f"  Technology Stack: {analysis.technology_stack}")
        
        if analysis.identified_components:
            print(f"  Identified Components:")
            for component in analysis.identified_components:
                print(f"    - {component.name} ({component.category}): {component.description}")
        
        if analysis.follow_up_questions:
            print(f"  Follow-up Questions:")
            for i, question in enumerate(analysis.follow_up_questions[:3], 1):  # Show first 3
                print(f"    {i}. {question}")
        
        if analysis.risk_factors:
            print(f"  Risk Factors:")
            for risk in analysis.risk_factors[:3]:  # Show first 3
                print(f"    - {risk}")
        print("  ── End of Analysis ──")
    
    def _display_project_plan(self, project_plan: Dict[str, Any]) -> None:
        """Display the project planning results."""
        print(f"\n📋 PROJECT PLAN:")
        
        if "project_structure" in project_plan:
            structure = project_plan["project_structure"]
            print(f"  Project Name: {structure.project_name}")
            print(f"  Root Directory: {structure.root_directory}")
            print(f"  Total Files: {len(structure.files)}")
            print(f"  Directories: {len(structure.directories)}")
        
        if "development_roadmap" in project_plan:
            roadmap = project_plan["development_roadmap"]
            if hasattr(roadmap, 'phases') and roadmap.phases:
                print(f"  Development Phases: {len(roadmap.phases)}")
                if hasattr(roadmap, 'total_estimated_time'):
                    print(f"  Total Estimated Time: {roadmap.total_estimated_time}")
                
                for i, phase in enumerate(roadmap.phases, 1):
                    if hasattr(phase, 'name') and hasattr(phase, 'estimated_time'):
                        print(f"    Phase {i}: {phase.name} - {phase.estimated_time}")
        
        if "technology_recommendations" in project_plan:
            recommendations = project_plan["technology_recommendations"]
            if recommendations.get("libraries"):
                print(f"  Recommended Libraries: {', '.join(recommendations['libraries'][:3])}")
        print("  ── End of Plan ──")
    
    def get_session_status(self) -> Dict[str, Any]:
        """Get the status of the current session."""
        if not self.current_session:
            return {"status": "no_active_session"}
        
        return {
            "session_id": self.current_session.session_id,
            "status": self.current_session.execution_status,
            "total_time": self.current_session.total_time,
            "success": self.current_session.success,
            "original_request": self.current_session.original_request[:100] + "..."
        }
    
    def get_session_history(self) -> List[Dict[str, Any]]:
        """Get summary of session history."""
        history = []
        for session in self.session_history[-10:]:  # Last 10 sessions
            history.append({
                "session_id": session.session_id,
                "timestamp": session.session_start_time,
                "success": session.success,
                "total_time": session.total_time,
                "request_preview": session.original_request[:50] + "..."
            })
        return history
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status."""
        return {
            "orchestrator_status": "running",
            "ai_providers": self.ai_provider_manager.get_provider_health(),
            "learning_system": self.learning_engine.get_learning_statistics(),
            "prompt_optimization": self.prompt_optimizer.get_optimization_stats(),
            "configuration": {
                "ai_providers_configured": bool(self.config.ai_providers.openai_api_key or 
                                              self.config.ai_providers.google_ai_api_key or 
                                              self.config.ai_providers.anthropic_api_key),
                "learning_enabled": self.config.learning.enable_learning,
                "cursor_integration": self.config.cursor.file_monitoring
            }
        }
    
    async def cleanup(self) -> None:
        """Cleanup resources and close connections."""
        print("🧹 Cleaning up orchestrator resources...")
        
        # Close database connections
        if hasattr(self.learning_engine, '_close_connections'):
            await self.learning_engine._close_connections()
        
        print("✅ Cleanup completed")
    
    def export_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Export data for a specific session."""
        for session in self.session_history:
            if session.session_id == session_id:
                return {
                    "session_id": session.session_id,
                    "original_request": session.original_request,
                    "analysis": {
                        "project_type": session.analysis.project_type,
                        "complexity_level": session.analysis.complexity_level,
                        "technology_stack": session.analysis.technology_stack,
                        "estimated_time": session.analysis.estimated_time
                    },
                    "project_plan": session.project_plan,
                    "optimized_prompts": [
                        {
                            "original_request": p.original_request,
                            "optimized_prompt": p.optimized_prompt,
                            "task_type": p.task_type,
                            "confidence_score": p.confidence_score
                        }
                        for p in session.optimized_prompts
                    ],
                    "execution_status": session.execution_status,
                    "total_time": session.total_time,
                    "success": session.success,
                    "metadata": session.metadata
                }
        return None
    
    async def run_interactive_mode(self) -> None:
        """Run the orchestrator in interactive mode."""
        print("\n🎮 Interactive Mode - AI Coding Orchestrator")
        print("Type 'quit' to exit, 'help' for commands")
        
        while True:
            try:
                user_input = input("\n🤖 Enter your coding request: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                elif user_input.lower() == 'help':
                    self._show_interactive_help()
                elif user_input.lower() == 'status':
                    self._show_system_status()
                elif user_input.lower() == 'history':
                    self._show_session_history()
                elif user_input.lower() == 'health':
                    self._show_system_health()
                elif user_input:
                    # Process the request
                    await self.orchestrate_request(user_input)
                else:
                    continue
                    
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")
        
        await self.cleanup()
    
    def _show_interactive_help(self) -> None:
        """Show help for interactive mode."""
        print("\n📚 Available Commands:")
        print("  help     - Show this help message")
        print("  status   - Show current session status")
        print("  history  - Show session history")
        print("  health   - Show system health")
        print("  quit     - Exit the orchestrator")
        print("\nOr simply type your coding request!")
    
    def _show_system_status(self) -> None:
        """Show current system status."""
        status = self.get_session_status()
        print(f"\n📊 Current Status: {status}")
    
    def _show_session_history(self) -> None:
        """Show session history."""
        history = self.get_session_history()
        print(f"\n📜 Session History (Last {len(history)} sessions):")
        for session in history:
            print(f"  {session['session_id']}: {session['request_preview']} - {'✅' if session['success'] else '❌'}")
    
    def _show_system_health(self) -> None:
        """Show system health information."""
        health = self.get_system_health()
        print(f"\n🏥 System Health:")
        print(f"  Orchestrator: {health['orchestrator_status']}")
        print(f"  AI Providers: {len(health['ai_providers'])} configured")
        print(f"  Learning System: {'✅' if health['learning_system']['total_attempts'] > 0 else '❌'}")
        print(f"  Prompt Optimization: {health['prompt_optimization']['total_optimizations']} attempts")
    
    def __str__(self) -> str:
        return f"OrchestratorMain(sessions={len(self.session_history)}, current={self.session_id})"
    
    def __repr__(self) -> str:
        return self.__str__()
