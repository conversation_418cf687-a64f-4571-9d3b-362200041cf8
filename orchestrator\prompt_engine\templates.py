"""
Prompt Templates for proven patterns in AI coding assistance.
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass

@dataclass
class PromptTemplate:
    """A prompt template with metadata."""
    name: str
    template: str
    description: str
    use_cases: List[str]
    success_rate: float
    tags: List[str]

class PromptTemplates:
    """Collection of proven prompt templates."""
    
    def __init__(self):
        self.templates = self._initialize_templates()
    
    def _initialize_templates(self) -> Dict[str, PromptTemplate]:
        """Initialize the prompt templates."""
        templates = {}
        
        # Code Generation Templates
        templates["code_generation"] = PromptTemplate(
            name="Code Generation",
            template="""Create a {language} {component_type} that {description}.

Requirements:
- {requirements}

Constraints:
- {constraints}

Please provide:
1. Complete, working code
2. Clear comments explaining the logic
3. Error handling where appropriate
4. Example usage if applicable

Follow {coding_standards} standards.""",
            description="Template for generating new code components",
            use_cases=["Creating new functions", "Building components", "Implementing features"],
            success_rate=0.85,
            tags=["code", "generation", "implementation"]
        )
        
        # Refactoring Templates
        templates["refactoring"] = PromptTemplate(
            name="Code Refactoring",
            template="""Refactor the following {language} code to improve {improvement_areas}:

{code_snippet}

Goals:
- {refactoring_goals}

Please provide:
1. Refactored code with improvements
2. Explanation of changes made
3. Benefits of the refactoring
4. Any trade-offs to consider

Maintain the same functionality while improving code quality.""",
            description="Template for refactoring existing code",
            use_cases=["Improving code quality", "Optimizing performance", "Enhancing readability"],
            success_rate=0.80,
            tags=["refactoring", "optimization", "improvement"]
        )
        
        # Debugging Templates
        templates["debugging"] = PromptTemplate(
            name="Debugging",
            template="""Help me debug this {language} code:

{code_snippet}

Error/Issue:
{error_description}

What I've tried:
{troubleshooting_steps}

Please help by:
1. Identifying the root cause
2. Suggesting specific fixes
3. Explaining why this happened
4. Providing prevention strategies
5. Testing the solution

Include code examples for the fixes.""",
            description="Template for debugging code issues",
            use_cases=["Fixing bugs", "Resolving errors", "Troubleshooting issues"],
            success_rate=0.75,
            tags=["debugging", "troubleshooting", "error-fixing"]
        )
        
        # Testing Templates
        templates["testing"] = PromptTemplate(
            name="Testing",
            template="""Create comprehensive tests for this {language} {component_type}:

{code_snippet}

Testing Requirements:
- {testing_requirements}

Test Coverage:
- {test_coverage_areas}

Please provide:
1. Unit tests covering main functionality
2. Edge case tests
3. Integration tests if applicable
4. Test data and fixtures
5. Clear test descriptions

Use {testing_framework} and follow testing best practices.""",
            description="Template for creating test suites",
            use_cases=["Unit testing", "Integration testing", "Test coverage"],
            success_rate=0.80,
            tags=["testing", "test-coverage", "quality-assurance"]
        )
        
        # Documentation Templates
        templates["documentation"] = PromptTemplate(
            name="Documentation",
            template="""Create comprehensive documentation for this {language} {component_type}:

{code_snippet}

Documentation Requirements:
- {documentation_requirements}

Target Audience:
- {target_audience}

Please provide:
1. Clear description of functionality
2. Usage examples with code
3. API reference if applicable
4. Installation/setup instructions
5. Troubleshooting section

Make it easy for other developers to understand and use.""",
            description="Template for creating documentation",
            use_cases=["API documentation", "README files", "Code comments"],
            success_rate=0.90,
            tags=["documentation", "explanation", "user-guide"]
        )
        
        # Architecture Templates
        templates["architecture"] = PromptTemplate(
            name="Architecture Design",
            template="""Design an architecture for {system_description}:

Requirements:
- {functional_requirements}
- {non_functional_requirements}

Constraints:
- {technical_constraints}
- {business_constraints}

Please provide:
1. High-level architecture diagram
2. Component breakdown and responsibilities
3. Data flow and interactions
4. Technology recommendations
5. Scalability and security considerations
6. Implementation roadmap

Focus on {architecture_principles} principles.""",
            description="Template for designing system architecture",
            use_cases=["System design", "Architecture planning", "Technical design"],
            success_rate=0.85,
            tags=["architecture", "design", "system-planning"]
        )
        
        # Code Review Templates
        templates["code_review"] = PromptTemplate(
            name="Code Review",
            template="""Review this {language} code for {review_focus}:

{code_snippet}

Review Criteria:
- {review_criteria}

Please provide:
1. Code quality assessment
2. Specific issues and suggestions
3. Security considerations
4. Performance implications
5. Best practices recommendations
6. Overall rating and summary

Be constructive and provide actionable feedback.""",
            description="Template for code review and feedback",
            use_cases=["Peer review", "Code quality assessment", "Security review"],
            success_rate=0.85,
            tags=["code-review", "feedback", "quality-assessment"]
        )
        
        # Performance Optimization Templates
        templates["performance"] = PromptTemplate(
            name="Performance Optimization",
            template="""Optimize this {language} code for better performance:

{code_snippet}

Current Performance Issues:
- {performance_issues}

Optimization Goals:
- {optimization_goals}

Please provide:
1. Performance analysis of current code
2. Specific optimization strategies
3. Optimized code implementation
4. Performance benchmarks and comparisons
5. Trade-offs and considerations
6. Monitoring and measurement suggestions

Focus on {optimization_priorities}.""",
            description="Template for performance optimization",
            use_cases=["Performance tuning", "Speed optimization", "Resource optimization"],
            success_rate=0.75,
            tags=["performance", "optimization", "efficiency"]
        )
        
        # Security Templates
        templates["security"] = PromptTemplate(
            name="Security Review",
            template="""Conduct a security review of this {language} code:

{code_snippet}

Security Context:
- {security_context}
- {threat_model}

Please identify:
1. Potential security vulnerabilities
2. Input validation issues
3. Authentication/authorization concerns
4. Data protection issues
5. Secure coding recommendations
6. Security testing suggestions

Provide specific fixes and security best practices.""",
            description="Template for security code review",
            use_cases=["Security audit", "Vulnerability assessment", "Secure coding"],
            success_rate=0.80,
            tags=["security", "vulnerability", "secure-coding"]
        )
        
        return templates
    
    def get_template(self, template_name: str) -> Optional[PromptTemplate]:
        """Get a specific template by name."""
        return self.templates.get(template_name)
    
    def get_templates_by_tag(self, tag: str) -> List[PromptTemplate]:
        """Get templates that match a specific tag."""
        return [template for template in self.templates.values() if tag in template.tags]
    
    def get_templates_by_use_case(self, use_case: str) -> List[PromptTemplate]:
        """Get templates suitable for a specific use case."""
        return [template for template in self.templates.values() if use_case in template.use_cases]
    
    def get_high_success_templates(self, threshold: float = 0.8) -> List[PromptTemplate]:
        """Get templates with success rate above threshold."""
        return [template for template in self.templates.values() if template.success_rate >= threshold]
    
    def get_all_templates(self) -> List[PromptTemplate]:
        """Get all available templates."""
        return list(self.templates.values())
    
    def search_templates(self, query: str) -> List[PromptTemplate]:
        """Search templates by name, description, or tags."""
        query_lower = query.lower()
        results = []
        
        for template in self.templates.values():
            if (query_lower in template.name.lower() or
                query_lower in template.description.lower() or
                any(query_lower in tag.lower() for tag in template.tags)):
                results.append(template)
        
        return results
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """Get statistics about all templates."""
        total_templates = len(self.templates)
        avg_success_rate = sum(t.success_rate for t in self.templates.values()) / total_templates
        
        tag_counts = {}
        for template in self.templates.values():
            for tag in template.tags:
                tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        return {
            "total_templates": total_templates,
            "average_success_rate": round(avg_success_rate, 3),
            "tag_distribution": tag_counts,
            "use_case_coverage": len(set(use_case for t in self.templates.values() for use_case in t.use_cases))
        }
    
    def customize_template(self, template_name: str, customizations: Dict[str, Any]) -> str:
        """Customize a template with specific values."""
        template = self.get_template(template_name)
        if not template:
            return ""
        
        customized = template.template
        
        # Replace placeholders with custom values
        for key, value in customizations.items():
            placeholder = "{" + key + "}"
            if placeholder in customized:
                customized = customized.replace(placeholder, str(value))
        
        return customized
    
    def create_custom_template(self, name: str, template: str, description: str, 
                             use_cases: List[str], tags: List[str]) -> PromptTemplate:
        """Create a custom template."""
        custom_template = PromptTemplate(
            name=name,
            template=template,
            description=description,
            use_cases=use_cases,
            success_rate=0.7,  # Default for custom templates
            tags=tags
        )
        
        self.templates[name] = custom_template
        return custom_template
    
    def export_templates(self) -> Dict[str, Any]:
        """Export all templates for external use."""
        export_data = {}
        for name, template in self.templates.items():
            export_data[name] = {
                "template": template.template,
                "description": template.description,
                "use_cases": template.use_cases,
                "success_rate": template.success_rate,
                "tags": template.tags
            }
        return export_data
    
    def import_templates(self, templates_data: Dict[str, Any]) -> int:
        """Import templates from external data."""
        imported_count = 0
        
        for name, data in templates_data.items():
            if name not in self.templates:  # Don't overwrite existing templates
                template = PromptTemplate(
                    name=name,
                    template=data["template"],
                    description=data["description"],
                    use_cases=data["use_cases"],
                    success_rate=data.get("success_rate", 0.7),
                    tags=data["tags"]
                )
                self.templates[name] = template
                imported_count += 1
        
        return imported_count
