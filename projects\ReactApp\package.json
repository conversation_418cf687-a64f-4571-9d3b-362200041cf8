{
  "name": "reactapp",
  "version": "1.0.0",
  "description": "Generated by AI Coding Orchestrator",
  "main": "src/index.js",
  "scripts": {
    "start": "node src/index.js",
    "dev": "nodemon src/index.js",
    "test": "jest",
    "build": "webpack --mode production"
  },
  "dependencies": {
        "react": "^1.0.0"
    "react-dom": "^1.0.0"
    "react-scripts": "^1.0.0"
  },
  "devDependencies": {
    "nodemon": "^2.0.22",
    "jest": "^29.5.0",
    "webpack": "^5.88.0",
    "webpack-cli": "^5.1.0"
  },
  "keywords": ["ai-generated", "coding-orchestrator"],
  "author": "AI Coding Orchestrator",
  "license": "MIT"
}
