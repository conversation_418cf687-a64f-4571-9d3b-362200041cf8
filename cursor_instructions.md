# AI Coding Orchestrator - Cursor Instructions

This file contains optimized prompts and project information for <PERSON>ursor.

## Project Overview
- **Project Name**: ReactApp
- **Root Directory**: projects/ReactApp
- **Project Location**: `projects/ReactApp`
- **Full Path**: `C:\Users\<USER>\ai-coding-orchestrator\projects\ReactApp`
- **Total Files**: 7

## Development Roadmap
- **Total Estimated Time**: 9 hours

### Phase 1: Project Setup
**Description**: Initialize project structure and dependencies
**Estimated Time**: 1-2 hours
**Priority**: high

**Tasks**:
- Create project directory structure
- Initialize version control (git)
- Set up development environment
- Install dependencies
- Configure basic project settings

**Deliverables**:
- Project structure
- Basic configuration
- Development environment

### Phase 2: Core Development
**Description**: Develop main application features
**Estimated Time**: 2-4 hours
**Priority**: high

**Tasks**:
- Set up frontend development environment
- Create main application layout
- Implement core UI components
- Set up state management
- Implement routing and navigation

**Deliverables**:
- Working application
- Core functionality

### Phase 3: Testing & Refinement
**Description**: Test application and refine functionality
**Estimated Time**: 2-4 hours
**Priority**: medium

**Tasks**:
- Write unit tests
- Integration testing
- User acceptance testing
- Bug fixes and refinements
- Performance optimization

**Deliverables**:
- Tested application
- Bug fixes
- Performance improvements

### Phase 4: Deployment
**Description**: Deploy application to production environment
**Estimated Time**: 1-2 hours
**Priority**: medium

**Tasks**:
- Prepare production build
- Set up production environment
- Configure environment variables
- Deploy application
- Verify deployment
- Set up monitoring

**Deliverables**:
- Deployed application
- Production environment

## Optimized Prompts for Cursor

### Prompt 1: architecture
**Confidence Score**: 1.00
**Provider**: gemini

**Original Request**:
```
Set up the project structure and initialize the development environment
```

**Optimized Prompt**:
```
Set up the project structure and initialize the development environment

Please design an architecture that:
- Is scalable and maintainable
- Follows design patterns and principles
- Considers performance and security
- Is well-documented with rationale

Please provide a modern, accessible, and responsive implementation.
Include clear structure, comments where non-obvious, and follow best practices.
Anticipate missing context by proposing sensible defaults.
```

### Prompt 2: architecture
**Confidence Score**: 1.00
**Provider**: gemini

**Original Request**:
```
Execute the Project Setup phase: Initialize project structure and dependencies
```

**Optimized Prompt**:
```
Execute the Project Setup phase: Initialize project structure and dependencies

Please design an architecture that:
- Is scalable and maintainable
- Follows design patterns and principles
- Considers performance and security
- Is well-documented with rationale

Please provide a modern, accessible, and responsive implementation.
Include clear structure, comments where non-obvious, and follow best practices.
Anticipate missing context by proposing sensible defaults.
```

### Prompt 3: general
**Confidence Score**: 0.80
**Provider**: gemini

**Original Request**:
```
Execute the Core Development phase: Develop main application features
```

**Optimized Prompt**:
```
Execute the Core Development phase: Develop main application features

Please provide a modern, accessible, and responsive implementation.
Include clear structure, comments where non-obvious, and follow best practices.
Anticipate missing context by proposing sensible defaults.
```

### Prompt 4: testing
**Confidence Score**: 1.00
**Provider**: gemini

**Original Request**:
```
Execute the Testing & Refinement phase: Test application and refine functionality
```

**Optimized Prompt**:
```
Execute the Testing & Refinement phase: Test application and refine functionality

Please create tests that:
- Cover the main functionality
- Include edge cases
- Are easy to understand and maintain
- Follow testing best practices

Please provide a modern, accessible, and responsive implementation.
Include clear structure, comments where non-obvious, and follow best practices.
Anticipate missing context by proposing sensible defaults.
```

### Prompt 5: general
**Confidence Score**: 0.90
**Provider**: gemini

**Original Request**:
```
Execute the Deployment phase: Deploy application to production environment
```

**Optimized Prompt**:
```
Execute the Deployment phase: Deploy application to production environment

Please provide a modern, accessible, and responsive implementation.
Include clear structure, comments where non-obvious, and follow best practices.
Anticipate missing context by proposing sensible defaults.
```

## Technology Recommendations

### Libraries
- lodash
- moment
- axios

### Tools
- eslint
- prettier
- husky

### Testing
- jest
- react-testing-library

### Deployment
- docker
- nginx

### Monitoring
- sentry
- logrocket

### Security
- helmet
- cors
- rate-limiting

## Usage Instructions

1. **Copy the optimized prompts** above and paste them into Cursor
2. **Follow the development roadmap** for structured development
3. **Reference the project structure** for file organization
4. **Use technology recommendations** for additional tools and libraries

---
*Generated by AI Coding Orchestrator*