"""
OpenAI Provider Implementation
Handles communication with OpenAI's GPT models
"""

import asyncio
from typing import Dict, Any, List
from .base_provider import AIProvider, AIRequest, AIResponse, TaskType
import openai

class OpenAIProvider(AIProvider):
    """OpenAI GPT provider implementation"""
    
    def _initialize(self):
        """Initialize OpenAI client"""
        try:
            openai.api_key = self.api_key
            self.client = openai.AsyncOpenAI(api_key=self.api_key)
            self.is_available = True
        except Exception as e:
            self.is_available = False
            print(f"Failed to initialize OpenAI provider: {e}")
    
    async def generate_response(self, request: AIRequest) -> AIResponse:
        """Generate response using OpenAI GPT"""
        try:
            # Prepare messages
            messages = []
            if request.system_message:
                messages.append({"role": "system", "content": request.system_message})
            
            messages.append({"role": "user", "content": request.prompt})
            
            # Prepare parameters
            params = {
                "model": self._get_best_model_for_task(request.task_type),
                "messages": messages,
                "temperature": request.temperature,
                "max_tokens": request.max_tokens or self.config.get("max_tokens", 4000)
            }
            
            # Make API call
            response = await self.client.chat.completions.create(**params)
            
            # Extract response content
            content = response.choices[0].message.content
            
            # Calculate cost (approximate)
            cost = self._calculate_cost(
                response.usage.prompt_tokens,
                response.usage.completion_tokens,
                params["model"]
            )
            
            return AIResponse(
                content=content,
                provider="openai",
                model=params["model"],
                tokens_used=response.usage.total_tokens,
                cost=cost,
                metadata={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "finish_reason": response.choices[0].finish_reason
                }
            )
            
        except Exception as e:
            raise Exception(f"OpenAI API error: {str(e)}")
    
    def get_models(self) -> List[str]:
        """Get available OpenAI models"""
        return [
            "gpt-4",
            "gpt-4-turbo-preview",
            "gpt-4-32k",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k"
        ]
    
    def estimate_cost(self, request: AIRequest) -> float:
        """Estimate cost for the request"""
        # Rough estimation based on prompt length and expected response
        estimated_prompt_tokens = len(request.prompt.split()) * 1.3  # Rough token estimation
        estimated_completion_tokens = request.max_tokens or 1000
        
        model = self._get_best_model_for_task(request.task_type)
        
        # Cost per 1K tokens (approximate)
        if "gpt-4" in model:
            cost_per_1k = 0.03 if "32k" in model else 0.06
        else:  # gpt-3.5-turbo
            cost_per_1k = 0.002
        
        total_cost = (estimated_prompt_tokens + estimated_completion_tokens) * cost_per_1k / 1000
        return round(total_cost, 4)
    
    def _get_best_model_for_task(self, task_type: TaskType) -> str:
        """Select the best model for the given task type"""
        if task_type in [TaskType.ANALYSIS, TaskType.PLANNING, TaskType.REVIEW]:
            return "gpt-4"  # Better reasoning for complex tasks
        elif task_type == TaskType.CODE_GENERATION:
            return "gpt-4"  # Better code generation
        elif task_type == TaskType.OPTIMIZATION:
            return "gpt-4"  # Better optimization
        else:
            return "gpt-3.5-turbo"  # Cost-effective for simpler tasks
    
    def _calculate_cost(self, prompt_tokens: int, completion_tokens: int, model: str) -> float:
        """Calculate actual cost based on token usage"""
        if "gpt-4" in model:
            if "32k" in model:
                prompt_cost = prompt_tokens * 0.06 / 1000
                completion_cost = completion_tokens * 0.12 / 1000
            else:
                prompt_cost = prompt_tokens * 0.03 / 1000
                completion_cost = completion_tokens * 0.06 / 1000
        else:  # gpt-3.5-turbo
            prompt_cost = prompt_tokens * 0.0015 / 1000
            completion_cost = completion_tokens * 0.002 / 1000
        
        return round(prompt_cost + completion_cost, 4)
    
    def is_suitable_for_task(self, task_type: TaskType) -> bool:
        """OpenAI is suitable for all task types"""
        return True
